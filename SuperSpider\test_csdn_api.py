#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CSDN API功能测试脚本
"""

import requests
import json

def test_csdn_parse():
    """测试CSDN解析功能"""
    print("=== 测试CSDN解析功能 ===")
    
    # 测试URL
    test_url = "https://blog.csdn.net/weixin_44799217/article/details/120550620"
    
    # 发送解析请求
    parse_url = "http://localhost:5000/api/csdn/parse"
    data = {
        "article_url": test_url
    }
    
    try:
        response = requests.post(parse_url, json=data, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"解析结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if result.get('success'):
                print("✅ CSDN解析测试成功")
                return result.get('data')
            else:
                print(f"❌ CSDN解析失败: {result.get('message')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    
    return None

def test_csdn_download():
    """测试CSDN下载功能"""
    print("\n=== 测试CSDN下载功能 ===")
    
    # 测试URL和邮箱
    test_url = "https://blog.csdn.net/weixin_44799217/article/details/120550620"
    test_email = "<EMAIL>"
    
    # 发送下载请求
    download_url = "http://localhost:5000/api/csdn/download"
    data = {
        "article_url": test_url,
        "email": test_email
    }
    
    try:
        response = requests.post(download_url, json=data, timeout=60)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"下载结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if result.get('success'):
                print("✅ CSDN下载测试成功")
                return True
            else:
                print(f"❌ CSDN下载失败: {result.get('message')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    
    return False

def test_api_status():
    """测试API状态"""
    print("\n=== 测试API状态 ===")
    
    try:
        response = requests.get("http://localhost:5000/api/status", timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"API状态: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            # 检查CSDN API是否在列表中
            apis = result.get('data', {}).get('apis', [])
            csdn_api = next((api for api in apis if api['name'] == 'csdn'), None)
            
            if csdn_api:
                print("✅ CSDN API已注册并运行")
                return True
            else:
                print("❌ CSDN API未找到")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    
    return False

def main():
    """主测试函数"""
    print("开始CSDN API功能测试...\n")
    
    # 测试API状态
    api_status_ok = test_api_status()
    
    if not api_status_ok:
        print("❌ API状态检查失败，停止测试")
        return
    
    # 测试解析功能
    parse_result = test_csdn_parse()
    
    # 测试下载功能
    download_result = test_csdn_download()
    
    # 汇总结果
    print(f"\n=== 测试结果汇总 ===")
    print(f"API状态: {'✅ 正常' if api_status_ok else '❌ 异常'}")
    print(f"解析功能: {'✅ 正常' if parse_result else '❌ 异常'}")
    print(f"下载功能: {'✅ 正常' if download_result else '❌ 异常'}")
    
    if api_status_ok and parse_result and download_result:
        print(f"\n🎉 所有测试通过！CSDN功能运行正常。")
    else:
        print(f"\n⚠️  部分测试失败，请检查相关配置。")
    
    print("\n注意事项:")
    print("1. 解析功能需要网络连接和有效的CSDN文章URL")
    print("2. 下载功能需要配置邮件服务才能正常发送PDF")
    print("3. 如果没有配置邮件服务，PDF会生成但不会发送")

if __name__ == "__main__":
    main()
