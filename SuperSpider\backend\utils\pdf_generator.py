#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
PDF生成工具
用于将文章内容生成PDF文件
"""

import os
import logging
from typing import Dict, Any, Optional
from datetime import datetime
import tempfile

logger = logging.getLogger(__name__)

class PDFGenerator:
    """PDF生成器"""
    
    def __init__(self, output_dir: str = None):
        """
        初始化PDF生成器
        
        Args:
            output_dir: 输出目录，如果为None则使用临时目录
        """
        self.output_dir = output_dir or tempfile.gettempdir()
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
    
    def generate_article_pdf(self, article_data: Dict[str, Any]) -> str:
        """
        生成文章PDF文件
        
        Args:
            article_data: 文章数据
            
        Returns:
            生成的PDF文件路径
        """
        try:
            # 尝试使用reportlab生成PDF
            return self._generate_with_reportlab(article_data)
        except ImportError:
            logger.warning("reportlab未安装，使用简单文本生成")
            return self._generate_simple_text(article_data)
        except Exception as e:
            logger.error(f"PDF生成失败: {e}")
            # 降级到简单文本生成
            return self._generate_simple_text(article_data)
    
    def _generate_with_reportlab(self, article_data: Dict[str, Any]) -> str:
        """
        使用reportlab生成PDF
        
        Args:
            article_data: 文章数据
            
        Returns:
            PDF文件路径
        """
        try:
            from reportlab.lib.pagesizes import A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            
            # 生成文件名
            title = article_data.get('title', '未知标题')
            safe_title = self._sanitize_filename(title)
            filename = f"{safe_title}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            filepath = os.path.join(self.output_dir, filename)
            
            # 创建PDF文档
            doc = SimpleDocTemplate(filepath, pagesize=A4)
            story = []
            
            # 获取样式
            styles = getSampleStyleSheet()
            
            # 标题样式
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1  # 居中
            )
            
            # 正文样式
            body_style = ParagraphStyle(
                'CustomBody',
                parent=styles['Normal'],
                fontSize=12,
                spaceAfter=12,
                leftIndent=0,
                rightIndent=0
            )
            
            # 添加标题
            if title:
                story.append(Paragraph(title, title_style))
                story.append(Spacer(1, 20))
            
            # 添加作者信息
            author = article_data.get('author', '')
            if author:
                story.append(Paragraph(f"作者: {author}", styles['Normal']))
                story.append(Spacer(1, 10))
            
            # 添加发布时间
            publish_time = article_data.get('publish_time', '')
            if publish_time:
                story.append(Paragraph(f"发布时间: {publish_time}", styles['Normal']))
                story.append(Spacer(1, 10))
            
            # 添加URL
            url = article_data.get('url', '')
            if url:
                story.append(Paragraph(f"原文链接: {url}", styles['Normal']))
                story.append(Spacer(1, 20))
            
            # 添加内容
            content = article_data.get('content', '')
            if content:
                # 将内容分段处理
                paragraphs = content.split('\n')
                for para in paragraphs:
                    if para.strip():
                        story.append(Paragraph(para.strip(), body_style))
                        story.append(Spacer(1, 6))
            
            # 生成PDF
            doc.build(story)
            
            logger.info(f"PDF生成成功: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"reportlab生成PDF失败: {e}")
            raise
    
    def _generate_simple_text(self, article_data: Dict[str, Any]) -> str:
        """
        生成简单文本文件（当PDF生成失败时的降级方案）
        
        Args:
            article_data: 文章数据
            
        Returns:
            文本文件路径
        """
        try:
            # 生成文件名
            title = article_data.get('title', '未知标题')
            safe_title = self._sanitize_filename(title)
            filename = f"{safe_title}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            filepath = os.path.join(self.output_dir, filename)
            
            # 写入文本文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"标题: {article_data.get('title', '')}\n")
                f.write(f"作者: {article_data.get('author', '')}\n")
                f.write(f"发布时间: {article_data.get('publish_time', '')}\n")
                f.write(f"原文链接: {article_data.get('url', '')}\n")
                f.write("\n" + "="*50 + "\n\n")
                f.write(article_data.get('content', ''))
            
            logger.info(f"文本文件生成成功: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"文本文件生成失败: {e}")
            raise
    
    def _sanitize_filename(self, filename: str) -> str:
        """
        清理文件名，移除不合法字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            清理后的文件名
        """
        # 移除或替换不合法字符
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # 限制长度
        if len(filename) > 100:
            filename = filename[:100]
        
        return filename.strip()
    
    def cleanup_old_files(self, max_age_hours: int = 24):
        """
        清理旧文件
        
        Args:
            max_age_hours: 文件最大保存时间（小时）
        """
        try:
            current_time = datetime.now()
            
            for filename in os.listdir(self.output_dir):
                filepath = os.path.join(self.output_dir, filename)
                
                # 检查文件修改时间
                file_time = datetime.fromtimestamp(os.path.getmtime(filepath))
                age_hours = (current_time - file_time).total_seconds() / 3600
                
                if age_hours > max_age_hours:
                    try:
                        os.remove(filepath)
                        logger.info(f"清理旧文件: {filepath}")
                    except Exception as e:
                        logger.warning(f"清理文件失败 {filepath}: {e}")
                        
        except Exception as e:
            logger.error(f"清理旧文件失败: {e}")

def create_pdf_generator(output_dir: str = None) -> PDFGenerator:
    """
    创建PDF生成器实例
    
    Args:
        output_dir: 输出目录
        
    Returns:
        PDF生成器实例
    """
    return PDFGenerator(output_dir)
