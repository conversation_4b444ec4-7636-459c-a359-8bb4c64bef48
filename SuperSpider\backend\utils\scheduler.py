#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
定时任务调度器
处理系统定期任务，如清理过期用户等
"""

import logging
import threading
import time
from datetime import datetime, timedelta

from backend.utils.permissions import PermissionManager

# 创建日志记录器
logger = logging.getLogger(__name__)

class TaskScheduler:
    """任务调度器"""

    def __init__(self):
        self.running = False
        self.thread = None
        self.tasks = []

    def add_task(self, func, interval_hours=24, name="Unknown Task"):
        """
        添加定时任务

        Args:
            func: 要执行的函数
            interval_hours: 执行间隔（小时）
            name: 任务名称
        """
        task = {
            'func': func,
            'interval': timedelta(hours=interval_hours),
            'name': name,
            'last_run': None,
            'next_run': datetime.now()
        }
        self.tasks.append(task)
        logger.info(f"添加定时任务: {name}, 间隔: {interval_hours}小时")

    def start(self):
        """启动调度器"""
        if self.running:
            logger.warning("调度器已在运行")
            return

        self.running = True
        self.thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.thread.start()
        logger.info("任务调度器已启动")

    def stop(self):
        """停止调度器"""
        self.running = False
        if self.thread:
            self.thread.join()
        logger.info("任务调度器已停止")

    def _run_scheduler(self):
        """调度器主循环"""
        while self.running:
            try:
                current_time = datetime.now()

                for task in self.tasks:
                    if current_time >= task['next_run']:
                        try:
                            logger.info(f"执行定时任务: {task['name']}")
                            result = task['func']()
                            task['last_run'] = current_time
                            task['next_run'] = current_time + task['interval']
                            logger.info(f"任务 {task['name']} 执行完成，结果: {result}")
                        except Exception as e:
                            logger.error(f"任务 {task['name']} 执行失败: {e}")
                            # 即使失败也要更新下次执行时间，避免一直重试
                            task['next_run'] = current_time + task['interval']

                # 每分钟检查一次
                time.sleep(60)

            except Exception as e:
                logger.error(f"调度器运行异常: {e}")
                time.sleep(60)

# 全局调度器实例
scheduler = TaskScheduler()

# 全局应用实例引用
_app_instance = None

def set_app_instance(app):
    """设置应用实例引用"""
    global _app_instance
    _app_instance = app

def cleanup_expired_users_task():
    """清理过期用户的定时任务"""
    try:
        if _app_instance is None:
            logger.warning("应用实例未设置，跳过清理任务")
            return "应用实例未设置"

        with _app_instance.app_context():
            downgraded_count = PermissionManager.check_and_downgrade_expired_users()
            return f"降级了 {downgraded_count} 个过期用户"
    except Exception as e:
        logger.error(f"清理过期用户任务失败: {e}")
        return f"任务失败: {e}"

def init_scheduler():
    """初始化调度器和任务"""
    # 添加清理过期用户任务，每6小时执行一次
    scheduler.add_task(
        func=cleanup_expired_users_task,
        interval_hours=6,
        name="清理过期Pro用户"
    )

    # 启动调度器
    scheduler.start()
    logger.info("定时任务调度器初始化完成")

def shutdown_scheduler():
    """关闭调度器"""
    scheduler.stop()
    logger.info("定时任务调度器已关闭")
