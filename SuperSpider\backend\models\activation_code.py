#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
激活码模型
用于Pro用户升级的卡密系统
"""

import datetime
import secrets
import string
from .user import db


class ActivationCode(db.Model):
    """激活码模型"""
    __tablename__ = 'activation_codes'

    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(32), unique=True, index=True, nullable=False)  # 激活码
    days = db.Column(db.Integer, nullable=False)  # 激活天数
    is_used = db.Column(db.<PERSON><PERSON>an, default=False, nullable=False)  # 是否已使用
    created_at = db.Column(db.DateTime, default=datetime.datetime.now, nullable=False)  # 创建时间
    used_at = db.Column(db.DateTime)  # 使用时间
    used_by = db.Column(db.Integer, db.<PERSON><PERSON>('users.id'))  # 使用者ID
    created_by = db.Column(db.<PERSON>te<PERSON>, db.<PERSON><PERSON>ey('users.id'), nullable=False)  # 创建者ID
    description = db.Column(db.String(255))  # 描述信息

    # 关联关系
    user_used = db.relationship('User', foreign_keys=[used_by], backref='used_codes')
    user_created = db.relationship('User', foreign_keys=[created_by], backref='created_codes')

    def __init__(self, days, created_by, description=None):
        """初始化激活码"""
        self.code = self.generate_code()
        self.days = days
        self.created_by = created_by
        self.description = description

    @staticmethod
    def generate_code():
        """生成激活码"""
        # 生成16位随机字符串，包含大小写字母和数字
        characters = string.ascii_uppercase + string.digits
        # 排除容易混淆的字符
        characters = characters.replace('0', '').replace('O', '').replace('1', '').replace('I', '').replace('L', '')

        code = ''.join(secrets.choice(characters) for _ in range(16))

        # 格式化为 XXXX-XXXX-XXXX-XXXX
        formatted_code = '-'.join([code[i:i+4] for i in range(0, 16, 4)])

        return formatted_code

    def use_code(self, user_id):
        """使用激活码"""
        if self.is_used:
            return False, "激活码已被使用"

        self.is_used = True
        self.used_at = datetime.datetime.now()
        self.used_by = user_id

        return True, "激活码使用成功"

    def to_dict(self, include_sensitive=False):
        """转换为字典"""
        data = {
            'id': self.id,
            'days': self.days,
            'is_used': self.is_used,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'used_at': self.used_at.isoformat() if self.used_at else None,
            'description': self.description
        }

        # 只有管理员才能看到完整的激活码
        if include_sensitive:
            data['code'] = self.code
            data['used_by'] = self.used_by
            data['created_by'] = self.created_by
        else:
            # 普通用户只能看到部分信息
            if self.code:
                data['code_preview'] = self.code[:4] + '-****-****-' + self.code[-4:]

        return data

    def get_status_display(self):
        """获取状态显示文本"""
        if self.is_used:
            return f"已使用 ({self.used_at.strftime('%Y-%m-%d %H:%M') if self.used_at else '未知时间'})"
        else:
            return "未使用"

    def get_days_display(self):
        """获取天数显示文本"""
        if self.days == 1:
            return "1天"
        elif self.days == 7:
            return "7天"
        elif self.days == 30:
            return "30天"
        elif self.days == 90:
            return "90天"
        elif self.days == 365:
            return "365天"
        else:
            return f"{self.days}天"

    @classmethod
    def get_unused_codes(cls):
        """获取所有未使用的激活码"""
        return cls.query.filter_by(is_used=False).all()

    @classmethod
    def get_codes_by_creator(cls, creator_id):
        """获取指定创建者的激活码"""
        return cls.query.filter_by(created_by=creator_id).all()

    @classmethod
    def find_by_code(cls, code):
        """根据激活码查找"""
        return cls.query.filter_by(code=code).first()

    @classmethod
    def get_usage_stats(cls):
        """获取使用统计"""
        total = cls.query.count()
        used = cls.query.filter_by(is_used=True).count()
        unused = total - used

        return {
            'total': total,
            'used': used,
            'unused': unused,
            'usage_rate': round((used / total * 100) if total > 0 else 0, 2)
        }

    def __repr__(self):
        """字符串表示"""
        return f'<ActivationCode {self.code} ({self.get_days_display()})>'
