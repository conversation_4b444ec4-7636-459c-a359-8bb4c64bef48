#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
管理员API模块
提供用户管理和系统管理功能
"""

import logging
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user
from sqlalchemy import desc, func

from backend.main import db
from backend.models.user import User
from backend.models.search_record import SearchRecord

# 创建日志记录器
logger = logging.getLogger(__name__)

# 创建蓝图
admin_api = Blueprint('admin_api', __name__, url_prefix='/admin')

# 管理员权限检查装饰器
def admin_required(f):
    """检查用户是否为管理员"""
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            return jsonify({
                "success": False,
                "message": "需要管理员权限",
                "data": None
            }), 403
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return login_required(decorated_function)

@admin_api.route('/users', methods=['GET'])
@admin_required
def get_users():
    """
    获取用户列表

    查询参数:
        page: 页码，默认1
        per_page: 每页记录数，默认10
        username: 用户名筛选，可选
        role: 角色筛选，可选

    响应:
        {
            "success": true,
            "message": "获取成功",
            "data": {
                "users": [
                    {
                        "id": 1,
                        "username": "admin",
                        "email": "<EMAIL>",
                        "role": "admin",
                        "created_at": "2023-01-01T12:00:00"
                    }
                ],
                "total": 100,
                "page": 1,
                "per_page": 10,
                "pages": 10
            }
        }
    """
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        username = request.args.get('username')
        role = request.args.get('role')

        # 构建查询
        query = User.query

        # 应用筛选条件
        if username:
            query = query.filter(User.username.like(f'%{username}%'))
        if role:
            query = query.filter_by(role=role)

        # 按创建时间降序排序
        query = query.order_by(desc(User.created_at))

        # 分页
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        users = pagination.items

        # 构建响应数据
        user_list = [user.to_dict(include_email=True) for user in users]

        return jsonify({
            "success": True,
            "message": "获取成功",
            "data": {
                "users": user_list,
                "total": pagination.total,
                "page": page,
                "per_page": per_page,
                "pages": pagination.pages
            }
        })

    except Exception as e:
        logger.error(f"获取用户列表失败: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"获取失败: {str(e)}",
            "data": None
        }), 500

@admin_api.route('/users/<int:user_id>', methods=['GET'])
@admin_required
def get_user(user_id):
    """
    获取用户详情

    路径参数:
        user_id: 用户ID

    响应:
        {
            "success": true,
            "message": "获取成功",
            "data": {
                "user": {
                    "id": 1,
                    "username": "admin",
                    "email": "<EMAIL>",
                    "role": "admin",
                    "created_at": "2023-01-01T12:00:00"
                }
            }
        }
    """
    try:
        user = User.query.get(user_id)

        if not user:
            return jsonify({
                "success": False,
                "message": "用户不存在",
                "data": None
            }), 404

        return jsonify({
            "success": True,
            "message": "获取成功",
            "data": {
                "user": user.to_dict(include_email=True)
            }
        })

    except Exception as e:
        logger.error(f"获取用户详情失败: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"获取失败: {str(e)}",
            "data": None
        }), 500

@admin_api.route('/users/<int:user_id>', methods=['PUT'])
@admin_required
def update_user(user_id):
    """
    更新用户信息

    路径参数:
        user_id: 用户ID

    请求体:
        {
            "role": "admin",
            "is_active": true
        }

    响应:
        {
            "success": true,
            "message": "更新成功",
            "data": {
                "user": {
                    "id": 1,
                    "username": "admin",
                    "email": "<EMAIL>",
                    "role": "admin",
                    "created_at": "2023-01-01T12:00:00"
                }
            }
        }
    """
    try:
        user = User.query.get(user_id)

        if not user:
            return jsonify({
                "success": False,
                "message": "用户不存在",
                "data": None
            }), 404

        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "message": "无效的请求数据",
                "data": None
            }), 400

        # 可更新的字段
        allowed_fields = ['role', 'is_active', 'is_admin']

        # 更新用户信息
        for field in allowed_fields:
            if field in data:
                setattr(user, field, data[field])

        db.session.commit()

        return jsonify({
            "success": True,
            "message": "更新成功",
            "data": {
                "user": user.to_dict(include_email=True)
            }
        })

    except Exception as e:
        logger.error(f"更新用户信息失败: {str(e)}")
        db.session.rollback()
        return jsonify({
            "success": False,
            "message": f"更新失败: {str(e)}",
            "data": None
        }), 500

@admin_api.route('/stats', methods=['GET'])
@admin_required
def get_system_stats():
    """
    获取系统统计信息

    响应:
        {
            "success": true,
            "message": "获取成功",
            "data": {
                "user_count": 100,
                "download_count": 500,
                "platform_stats": {
                    "kuaishou": 300,
                    "csdn": 200
                },
                "recent_users": 10,
                "recent_downloads": 50
            }
        }
    """
    try:
        # 用户总数
        user_count = User.query.count()

        # 搜索总数
        search_count = SearchRecord.query.count()

        # 平台统计
        platform_stats = {}
        platforms = db.session.query(SearchRecord.platform, func.count(SearchRecord.id))\
            .group_by(SearchRecord.platform)\
            .all()

        for platform, count in platforms:
            platform_stats[platform] = count

        # 最近注册用户数（7天内）
        recent_date = datetime.now() - timedelta(days=7)
        recent_users = User.query.filter(User.created_at >= recent_date).count()

        # 最近搜索数（7天内）
        recent_searches = SearchRecord.query.filter(SearchRecord.created_at >= recent_date).count()

        return jsonify({
            "success": True,
            "message": "获取成功",
            "data": {
                "user_count": user_count,
                "search_count": search_count,
                "platform_stats": platform_stats,
                "recent_users": recent_users,
                "recent_searches": recent_searches
            }
        })

    except Exception as e:
        logger.error(f"获取系统统计信息失败: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"获取失败: {str(e)}",
            "data": None
        }), 500
