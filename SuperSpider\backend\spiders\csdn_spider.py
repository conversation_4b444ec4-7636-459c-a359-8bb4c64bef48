#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CSDN爬虫类
用于解析CSDN文章内容
"""

import re
import logging
import requests
from typing import Dict, Any, Optional
from urllib.parse import urlparse, parse_qs
from bs4 import BeautifulSoup
import time
import json

from .base_spider import BaseSpider

logger = logging.getLogger(__name__)

class CSDNSpider(BaseSpider):
    """CSDN文章爬虫"""
    
    def __init__(self):
        super().__init__("CSDN爬虫")
        
        # 设置请求头，模拟浏览器访问
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        # 创建session以保持连接
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行CSDN文章解析
        
        Args:
            params: 包含article_url的参数字典
            
        Returns:
            解析结果
        """
        article_url = params.get('article_url')
        
        if not article_url:
            raise ValueError("缺少article_url参数")
        
        # 验证URL格式
        if not self._is_valid_csdn_url(article_url):
            raise ValueError("无效的CSDN文章URL")
        
        # 解析文章
        article_data = self._parse_article(article_url)
        
        return {
            "success": True,
            "data": article_data
        }
    
    def _is_valid_csdn_url(self, url: str) -> bool:
        """
        验证是否为有效的CSDN文章URL
        
        Args:
            url: 待验证的URL
            
        Returns:
            是否有效
        """
        try:
            parsed = urlparse(url)
            # 检查域名是否为CSDN
            if 'csdn.net' not in parsed.netloc:
                return False
            
            # 检查路径是否包含文章标识
            if '/article/details/' not in parsed.path and '/blog/details/' not in parsed.path:
                return False
            
            return True
        except Exception:
            return False
    
    def _parse_article(self, url: str) -> Dict[str, Any]:
        """
        解析CSDN文章内容
        
        Args:
            url: 文章URL
            
        Returns:
            文章数据
        """
        try:
            logger.info(f"开始解析CSDN文章: {url}")
            
            # 发送请求获取页面内容
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取文章信息
            article_data = self._extract_article_info(soup, url)
            
            logger.info(f"成功解析CSDN文章: {article_data.get('title', '未知标题')}")
            
            return article_data
            
        except requests.RequestException as e:
            logger.error(f"请求CSDN文章失败: {e}")
            raise Exception(f"无法访问文章页面: {e}")
        except Exception as e:
            logger.error(f"解析CSDN文章失败: {e}")
            raise Exception(f"解析文章内容失败: {e}")
    
    def _extract_article_info(self, soup: BeautifulSoup, url: str) -> Dict[str, Any]:
        """
        从HTML中提取文章信息
        
        Args:
            soup: BeautifulSoup对象
            url: 文章URL
            
        Returns:
            文章信息字典
        """
        article_data = {
            "url": url,
            "title": "",
            "author": "",
            "content": "",
            "publish_time": "",
            "read_count": "",
            "tags": [],
            "category": ""
        }
        
        try:
            # 提取标题
            title_elem = soup.find('h1', class_='title-article') or soup.find('h1', id='articleTitle')
            if title_elem:
                article_data["title"] = title_elem.get_text().strip()
            
            # 提取作者信息
            author_elem = soup.find('a', class_='follow-nickName') or soup.find('div', class_='user-info')
            if author_elem:
                article_data["author"] = author_elem.get_text().strip()
            
            # 提取发布时间
            time_elem = soup.find('span', class_='time')
            if time_elem:
                article_data["publish_time"] = time_elem.get_text().strip()
            
            # 提取阅读数
            read_elem = soup.find('span', class_='read-count')
            if read_elem:
                article_data["read_count"] = read_elem.get_text().strip()
            
            # 提取文章内容
            content_elem = soup.find('div', id='content_views') or soup.find('article', class_='baidu_pl')
            if content_elem:
                # 清理内容，移除不必要的元素
                self._clean_content(content_elem)
                article_data["content"] = content_elem.get_text().strip()
            
            # 提取标签
            tag_elems = soup.find_all('a', class_='tag-link')
            if tag_elems:
                article_data["tags"] = [tag.get_text().strip() for tag in tag_elems]
            
            # 提取分类
            category_elem = soup.find('div', class_='article-info-box')
            if category_elem:
                category_link = category_elem.find('a')
                if category_link:
                    article_data["category"] = category_link.get_text().strip()
            
        except Exception as e:
            logger.warning(f"提取文章信息时出现警告: {e}")
        
        return article_data
    
    def _clean_content(self, content_elem):
        """
        清理文章内容，移除不必要的元素
        
        Args:
            content_elem: 内容元素
        """
        # 移除广告、脚本等不需要的元素
        for elem in content_elem.find_all(['script', 'style', 'iframe', 'ins']):
            elem.decompose()
        
        # 移除特定的广告类
        for elem in content_elem.find_all(class_=['ad', 'advertisement', 'csdn-tracking-statistics']):
            elem.decompose()
    
    def get_article_id(self, url: str) -> Optional[str]:
        """
        从URL中提取文章ID
        
        Args:
            url: 文章URL
            
        Returns:
            文章ID
        """
        try:
            # 从URL路径中提取文章ID
            match = re.search(r'/article/details/(\d+)', url)
            if match:
                return match.group(1)
            
            match = re.search(r'/blog/details/(\d+)', url)
            if match:
                return match.group(1)
            
            return None
        except Exception:
            return None
    
    def format_content_for_display(self, content: str, max_length: int = 500) -> str:
        """
        格式化内容用于显示
        
        Args:
            content: 原始内容
            max_length: 最大长度
            
        Returns:
            格式化后的内容
        """
        if not content:
            return ""
        
        # 移除多余的空白字符
        content = re.sub(r'\s+', ' ', content).strip()
        
        # 截断过长的内容
        if len(content) > max_length:
            content = content[:max_length] + "..."
        
        return content
