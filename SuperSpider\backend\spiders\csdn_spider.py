#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CSDN文章爬虫
用于解析CSDN博客文章内容
"""

import logging
import requests
import traceback
from bs4 import BeautifulSoup
from typing import Dict, Any
from .base_spider import BaseSpider

logger = logging.getLogger(__name__)


class CSDNSpider(BaseSpider):
    """CSDN文章爬虫"""

    def __init__(self):
        super().__init__("CSDN爬虫")
        self.platform = "csdn"

        # 配置请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-C<PERSON>,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }

    def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行CSDN文章解析

        Args:
            params: 包含article_url的参数字典

        Returns:
            Dict: 解析结果
        """
        try:
            article_url = params.get("article_url")
            if not article_url:
                return {
                    "success": False,
                    "message": "请提供文章URL",
                    "data": None
                }

            logger.info(f"开始解析CSDN文章: {article_url}")

            # 解析文章
            article_data = self._parse_article(article_url)

            if article_data:
                logger.info(f"成功解析CSDN文章: {article_data.get('title', '未知标题')}")
                return {
                    "success": True,
                    "message": "解析成功",
                    "data": article_data
                }
            else:
                return {
                    "success": False,
                    "message": "解析失败",
                    "data": None
                }

        except Exception as e:
            logger.error(f"CSDN爬虫执行失败: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                "success": False,
                "message": f"解析失败: {str(e)}",
                "data": None
            }

    def _parse_article(self, url: str) -> Dict[str, Any]:
        """
        解析CSDN文章内容

        Args:
            url: 文章URL

        Returns:
            Dict: 文章数据
        """
        try:
            # 发送请求
            response = requests.get(url, headers=self.headers, timeout=30)
            response.raise_for_status()

            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取文章标题
            title_elem = soup.find('h1', class_='title-article') or soup.find('h1')
            title = title_elem.get_text(strip=True) if title_elem else "未知标题"

            # 提取作者信息
            author_elem = soup.find('a', class_='follow-nickName') or soup.find('div', class_='user-info')
            author = author_elem.get_text(strip=True) if author_elem else "未知作者"

            # 提取文章内容
            content_elem = soup.find('div', id='content_views') or soup.find('article')
            if content_elem:
                # 清理不需要的元素
                for elem in content_elem.find_all(['script', 'style', 'iframe']):
                    elem.decompose()
                content = content_elem.get_text(strip=True)
            else:
                content = "无法获取文章内容"

            # 提取发布时间
            time_elem = soup.find('span', class_='time')
            publish_time = time_elem.get_text(strip=True) if time_elem else "未知时间"

            return {
                "title": title,
                "author": author,
                "content": content,
                "publish_time": publish_time,
                "url": url,
                "platform": "CSDN"
            }

        except requests.RequestException as e:
            logger.error(f"请求CSDN文章失败: {e}")
            raise Exception(f"无法访问文章页面: {e}")
        except Exception as e:
            logger.error(f"解析CSDN文章失败: {e}")
            raise Exception(f"文章解析失败: {e}")

    def format_content_for_display(self, content: str, max_length: int = 500) -> str:
        """
        格式化内容用于前端显示

        Args:
            content: 原始内容
            max_length: 最大长度

        Returns:
            str: 格式化后的内容
        """
        if not content:
            return ""

        # 截取指定长度
        if len(content) > max_length:
            return content[:max_length] + "..."
        return content

    def check_status(self) -> Dict[str, Any]:
        """
        检查CSDN爬虫状态

        Returns:
            Dict: 状态信息
        """
        return {
            "name": self.name,
            "platform": self.platform,
            "status": "ready",
            "version": "2.0.0"
        }