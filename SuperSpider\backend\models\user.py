#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用户模型
处理用户账户相关数据
"""

import datetime
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from flask_sqlalchemy import SQLAlchemy

# 创建数据库实例
db = SQLAlchemy()

class User(db.Model, UserMixin):
    """用户模型类"""
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, index=True, nullable=False)
    phone = db.Column(db.String(20), unique=True, index=True, nullable=False)  # 手机号字段，必填且唯一
    password_hash = db.Column(db.String(255), nullable=False)  # 增加长度以适应不同的哈希算法
    created_at = db.Column(db.DateTime, default=datetime.datetime.now, nullable=False)
    last_login = db.Column(db.DateTime, default=datetime.datetime.now, nullable=False)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    is_admin = db.Column(db.Boolean, default=False, nullable=False)
    login_count = db.Column(db.Integer, default=0, nullable=False)  # 登录次数
    role = db.Column(db.String(20), default='normal_user', nullable=False)  # 用户角色: normal_user, pro_user, super_admin
    permissions = db.Column(db.Text)  # JSON格式存储具体权限
    vip_expire_date = db.Column(db.DateTime)  # Pro用户到期时间
    is_banned = db.Column(db.Boolean, default=False, nullable=False)  # 是否被封禁

    def __init__(self, username, password, phone=None, **kwargs):
        """初始化用户"""
        self.username = username
        self.password = password
        self.phone = phone
        for key, value in kwargs.items():
            setattr(self, key, value)

    @property
    def password(self):
        """密码不允许直接读取"""
        raise AttributeError('密码不可读')

    @password.setter
    def password(self, password):
        """设置密码时自动哈希"""
        self.password_hash = generate_password_hash(password)

    def verify_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)

    def to_dict(self, include_sensitive=False):
        """
        转换为字典

        Args:
            include_sensitive: 是否包含敏感信息（手机号等）

        Returns:
            用户信息字典
        """
        data = {
            'id': self.id,
            'username': self.username,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'is_active': self.is_active,
            'is_admin': self.is_admin,
            'role': self.role,
            'login_count': self.login_count
        }

        # 仅在需要时包含敏感信息
        if include_sensitive:
            data['phone'] = self.phone

        return data

    def update_login_info(self):
        """更新登录信息"""
        self.last_login = datetime.datetime.now()
        self.login_count += 1  # 启用登录次数统计

    def get_permissions(self):
        """获取用户权限"""
        import json
        if self.permissions:
            try:
                return json.loads(self.permissions)
            except:
                return {}
        return self._get_default_permissions()

    def _get_default_permissions(self):
        """根据角色获取默认权限"""
        if self.role == 'super_admin':
            return {
                'video_platforms': ['douyin', 'kuaishou', 'bilibili'],
                'article_platforms': ['zhihu'],
                'admin_functions': ['user_management', 'system_settings', 'data_export'],
                'download_limit': -1,  # 无限制
                'api_rate_limit': -1   # 无限制
            }
        elif self.role == 'pro_user':
            return {
                'video_platforms': ['douyin', 'kuaishou', 'bilibili'],
                'article_platforms': ['zhihu'],
                'admin_functions': [],
                'download_limit': 1000,  # 每日1000次
                'api_rate_limit': 100    # 每分钟100次
            }
        else:  # normal_user
            return {
                'video_platforms': ['douyin', 'kuaishou', 'bilibili'],
                'article_platforms': [],
                'admin_functions': [],
                'download_limit': 50,   # 每日50次
                'api_rate_limit': 20    # 每分钟20次
            }

    def set_permissions(self, permissions):
        """设置用户权限"""
        import json
        self.permissions = json.dumps(permissions)

    def has_permission(self, permission_type, permission_value=None):
        """检查用户是否有特定权限"""
        if self.is_banned:
            return False

        permissions = self.get_permissions()

        if permission_type == 'platform':
            # 检查平台权限
            video_platforms = permissions.get('video_platforms', [])
            article_platforms = permissions.get('article_platforms', [])
            all_platforms = video_platforms + article_platforms
            return permission_value in all_platforms

        elif permission_type == 'admin':
            # 检查管理权限
            admin_functions = permissions.get('admin_functions', [])
            return permission_value in admin_functions

        elif permission_type == 'download_limit':
            # 检查下载限制
            limit = permissions.get('download_limit', 0)
            return limit == -1 or limit > 0

        return False

    def is_pro_user_valid(self):
        """检查Pro用户是否有效（未过期）"""
        if self.role != 'pro_user':
            return True
        if not self.vip_expire_date:
            return False
        return datetime.datetime.now() < self.vip_expire_date

    def get_role_display(self):
        """获取角色显示名称"""
        role_names = {
            'super_admin': '超级管理员',
            'pro_user': 'Pro用户',
            'normal_user': '普通用户'
        }
        return role_names.get(self.role, '未知角色')

    def __repr__(self):
        """字符串表示"""
        return f'<User {self.username} ({self.get_role_display()})>'