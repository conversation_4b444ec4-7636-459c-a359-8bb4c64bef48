#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
抖音API模块
提供抖音视频解析相关的API
"""

import logging
import traceback

from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user

from ..spiders.douyin_spider import <PERSON><PERSON><PERSON><PERSON><PERSON>er
from ..utils.permissions import require_permission
from ..models.search_record import SearchRecord
from ..main import db

# 创建日志记录器
logger = logging.getLogger(__name__)

# 创建蓝图
douyin_api = Blueprint('douyin_api', __name__, url_prefix='/douyin')


@douyin_api.route('/parse', methods=['POST'])
@login_required
@require_permission('platform', 'douyin')
def parse_video():
    """
    解析抖音视频链接

    请求体:
        {
            "video_url": "视频URL"
        }

    响应:
        {
            "success": true,
            "message": "解析成功",
            "data": {
                "title": "视频标题",
                "author": "作者名称",
                "videoUrl": "视频播放链接",
                "coverUrl": "封面图片链接",
                "description": "视频描述",
                "duration": "视频时长",
                "playCount": "播放次数",
                "likeCount": "点赞数",
                "commentCount": "评论数",
                "shareCount": "分享数"
            }
        }
    """
    try:
        # 验证请求参数
        if not request.is_json:
            return jsonify({
                "success": False,
                "message": "请求格式错误，需要JSON格式",
                "data": None
            }), 400

        data = request.json
        video_url = data.get('video_url')

        # 验证必要参数
        if not video_url:
            return jsonify({
                "success": False,
                "message": "请提供视频URL",
                "data": None
            }), 400

        # 创建抖音爬虫
        spider = DouyinSpider()

        # 执行解析任务
        result = spider.execute({
            "video_url": video_url,
            "download": False
        })

        # 如果解析成功，创建搜索记录
        if result["success"] and result.get("data"):
            try:
                video_data = result["data"]  # 抖音API返回对象格式

                # 检查是否已存在相同的搜索记录
                existing_record = SearchRecord.query.filter_by(
                    user_id=current_user.id,
                    content_url=video_url
                ).first()

                if existing_record:
                    # 如果记录已存在，增加搜索次数
                    existing_record.increment_search_count()
                    db.session.commit()
                else:
                    # 创建新的搜索记录
                    record = SearchRecord(
                        user_id=current_user.id,
                        platform='douyin',
                        content_type='video',
                        content_url=video_url,
                        title=video_data.get('title'),
                        author=video_data.get('author'),
                        video_url=video_data.get('videoUrl'),
                        thumbnail_url=video_data.get('coverUrl'),
                        duration=video_data.get('duration')
                    )
                    db.session.add(record)
                    db.session.commit()

                logger.info(f"用户 {current_user.username} 搜索抖音视频: {video_data.get('title', '未知标题')}")
            except Exception as e:
                logger.error(f"创建搜索记录失败: {str(e)}")
                # 不影响主要功能，继续返回解析结果

        return jsonify(result), 200 if result["success"] else 500

    except Exception as e:
        logger.error(f"解析视频失败: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "message": f"服务器错误: {str(e)}",
            "data": None
        }), 500


@douyin_api.route('/status', methods=['GET'])
def check_status():
    """
    检查抖音API服务状态

    响应:
        {
            "success": true,
            "message": "服务正常",
            "data": {
                "spider": {
                    "name": "抖音爬虫",
                    "platform": "douyin",
                    "status": "ready",
                    "version": "1.0.0"
                }
            }
        }
    """
    try:
        # 创建爬虫实例
        spider = DouyinSpider()
        spider_status = spider.check_status()

        return jsonify({
            "success": True,
            "message": "服务正常",
            "data": {
                "spider": spider_status
            }
        }), 200

    except Exception as e:
        logger.error(f"检查状态失败: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "message": f"服务异常: {str(e)}",
            "data": None
        }), 500


@douyin_api.route('/parse', methods=['GET'])
@login_required
@require_permission('platform', 'douyin')
def parse_video_get():
    """
    解析抖音视频链接（GET方法）

    查询参数:
        video_url: 视频URL

    响应:
        {
            "success": true,
            "message": "解析成功",
            "data": {
                "title": "视频标题",
                "author": "作者名称",
                "videoUrl": "视频播放链接",
                "coverUrl": "封面图片链接",
                "description": "视频描述",
                "duration": "视频时长",
                "playCount": "播放次数",
                "likeCount": "点赞数",
                "commentCount": "评论数",
                "shareCount": "分享数"
            }
        }
    """
    try:
        video_url = request.args.get('video_url')

        # 验证必要参数
        if not video_url:
            return jsonify({
                "success": False,
                "message": "请提供视频URL",
                "data": None
            }), 400

        # 创建抖音爬虫
        spider = DouyinSpider()

        # 执行解析任务
        result = spider.execute({
            "video_url": video_url,
            "download": False
        })

        # 如果解析成功，创建搜索记录
        if result["success"] and result.get("data"):
            try:
                video_data = result["data"]  # 抖音API返回对象格式

                # 检查是否已存在相同的搜索记录
                existing_record = SearchRecord.query.filter_by(
                    user_id=current_user.id,
                    content_url=video_url
                ).first()

                if existing_record:
                    # 如果记录已存在，增加搜索次数
                    existing_record.increment_search_count()
                    db.session.commit()
                else:
                    # 创建新的搜索记录
                    record = SearchRecord(
                        user_id=current_user.id,
                        platform='douyin',
                        content_type='video',
                        content_url=video_url,
                        title=video_data.get('title'),
                        author=video_data.get('author'),
                        video_url=video_data.get('videoUrl'),
                        thumbnail_url=video_data.get('coverUrl'),
                        duration=video_data.get('duration')
                    )
                    db.session.add(record)
                    db.session.commit()

                logger.info(f"用户 {current_user.username} 搜索抖音视频: {video_data.get('title', '未知标题')}")
            except Exception as e:
                logger.error(f"创建搜索记录失败: {str(e)}")
                # 不影响主要功能，继续返回解析结果

        return jsonify(result), 200 if result["success"] else 500

    except Exception as e:
        logger.error(f"解析视频失败: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "message": f"服务器错误: {str(e)}",
            "data": None
        }), 500