/**
 * CSDN功能相关JavaScript代码
 */

// 全局变量存储当前解析的文章数据
let currentArticleData = null;

/**
 * 初始化CSDN功能
 */
function initCSDNFeatures() {
    console.log('初始化CSDN功能...');

    // 绑定表单提交事件
    const csdnForm = document.getElementById('csdn-form');
    if (csdnForm) {
        csdnForm.addEventListener('submit', handleCSDNFormSubmit);
    }

    // 绑定下载按钮事件
    const downloadBtn = document.getElementById('csdn-download-pdf-btn');
    if (downloadBtn) {
        downloadBtn.addEventListener('click', handleCSDNDownload);
    }

    // 绑定截图按钮事件
    const screenshotBtn = document.getElementById('csdn-download-screenshot-btn');
    if (screenshotBtn) {
        screenshotBtn.addEventListener('click', handleCSDNScreenshot);
    }

    // 绑定复制内容按钮事件
    const copyBtn = document.getElementById('csdn-copy-content-btn');
    if (copyBtn) {
        copyBtn.addEventListener('click', handleCSDNCopyContent);
    }

    // 绑定结果关闭按钮事件
    const closeBtn = document.getElementById('csdn-result-close');
    if (closeBtn) {
        closeBtn.addEventListener('click', clearCSDNResults);
    }
}

/**
 * 处理CSDN表单提交
 */
async function handleCSDNFormSubmit(event) {
    event.preventDefault();

    const articleUrl = document.getElementById('csdn-url').value.trim();
    const email = document.getElementById('csdn-email').value.trim();

    if (!articleUrl) {
        showCSDNStatus('请输入CSDN文章链接', 'error');
        return;
    }

    if (!isValidCSDNUrl(articleUrl)) {
        showCSDNStatus('请输入有效的CSDN文章链接', 'error');
        return;
    }

    if (!email || !isValidEmail(email)) {
        showCSDNStatus('请输入有效的邮箱地址', 'error');
        return;
    }

    try {
        showCSDNStatus('正在解析文章...', 'info');
        showCSDNProgress(30);

        const response = await fetch('/api/csdn/parse', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                article_url: articleUrl
            })
        });

        const data = await response.json();

        if (data.success) {
            currentArticleData = data.data;
            showCSDNProgress(100);
            showCSDNStatus('解析成功！', 'success');
            displayCSDNResults(data.data);
        } else {
            showCSDNStatus(data.message || '解析失败', 'error');
        }

    } catch (error) {
        console.error('CSDN解析错误:', error);
        showCSDNStatus('网络错误，请稍后重试', 'error');
    } finally {
        hideCSDNProgress();
    }
}

/**
 * 处理CSDN文章下载
 */
async function handleCSDNDownload() {
    if (!currentArticleData) {
        showCSDNStatus('请先解析文章', 'error');
        return;
    }

    const email = document.getElementById('csdn-email').value.trim();

    if (!email || !isValidEmail(email)) {
        showCSDNStatus('请输入有效的邮箱地址', 'error');
        return;
    }

    try {
        showCSDNStatus('正在生成PDF并发送到邮箱...', 'info');
        showCSDNProgress(50);

        const response = await fetch('/api/csdn/download', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                article_url: currentArticleData.url,
                email: email
            })
        });

        const data = await response.json();

        if (data.success) {
            showCSDNProgress(100);
            showCSDNStatus(data.message || 'PDF已发送到您的邮箱', 'success');

            // 记录下载
            recordDownload('csdn', currentArticleData.title, currentArticleData.url, 'pdf');
        } else {
            showCSDNStatus(data.message || '下载失败', 'error');
        }

    } catch (error) {
        console.error('CSDN下载错误:', error);
        showCSDNStatus('网络错误，请稍后重试', 'error');
    } finally {
        hideCSDNProgress();
    }
}

/**
 * 处理CSDN文章截图
 */
async function handleCSDNScreenshot() {
    if (!currentArticleData) {
        showCSDNStatus('请先解析文章', 'error');
        return;
    }

    const email = document.getElementById('csdn-email').value.trim();

    if (!email || !isValidEmail(email)) {
        showCSDNStatus('请输入有效的邮箱地址', 'error');
        return;
    }

    try {
        showCSDNStatus('正在生成文章截图并发送到邮箱...', 'info');
        showCSDNProgress(50);

        const response = await fetch('/api/csdn/screenshot', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                article_url: currentArticleData.url,
                email: email
            })
        });

        const data = await response.json();

        if (data.success) {
            showCSDNProgress(100);
            showCSDNStatus(data.message || '截图已发送到您的邮箱', 'success');

            // 记录下载
            recordDownload('csdn', currentArticleData.title, currentArticleData.url, 'screenshot');
        } else {
            showCSDNStatus(data.message || '截图生成失败', 'error');
        }

    } catch (error) {
        console.error('CSDN截图错误:', error);
        showCSDNStatus('网络错误，请稍后重试', 'error');
    } finally {
        hideCSDNProgress();
    }
}

/**
 * 处理复制内容
 */
function handleCSDNCopyContent() {
    if (!currentArticleData || !currentArticleData.content) {
        showCSDNStatus('没有可复制的内容', 'error');
        return;
    }

    const content = `标题: ${currentArticleData.title || ''}\n作者: ${currentArticleData.author || ''}\n发布时间: ${currentArticleData.publish_time || ''}\n\n${currentArticleData.content || ''}`;

    if (navigator.clipboard) {
        navigator.clipboard.writeText(content).then(() => {
            showCSDNStatus('内容已复制到剪贴板', 'success');
        }).catch(() => {
            fallbackCopyText(content);
        });
    } else {
        fallbackCopyText(content);
    }
}

/**
 * 降级复制方法
 */
function fallbackCopyText(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();

    try {
        document.execCommand('copy');
        showCSDNStatus('内容已复制到剪贴板', 'success');
    } catch (err) {
        showCSDNStatus('复制失败，请手动复制', 'error');
    }

    document.body.removeChild(textArea);
}

/**
 * 显示CSDN解析结果
 */
function displayCSDNResults(data) {
    // 填充文章信息
    document.getElementById('csdn-article-title').textContent = data.title || '未知标题';
    document.getElementById('csdn-article-author').textContent = data.author || '未知作者';
    document.getElementById('csdn-article-time').textContent = data.publish_time || '未知时间';
    document.getElementById('csdn-article-reads').textContent = data.read_count || '未知';

    // 显示内容预览
    const contentElement = document.getElementById('csdn-article-content');
    const content = data.content_preview || data.content || '无内容预览';
    contentElement.textContent = content.length > 500 ? content.substring(0, 500) + '...' : content;

    // 显示结果容器
    const resultContainer = document.getElementById('csdn-result');
    if (resultContainer) {
        resultContainer.style.display = 'block';
        resultContainer.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }
}

/**
 * 显示CSDN状态信息
 */
function showCSDNStatus(message, type = 'info') {
    const statusContainer = document.getElementById('csdn-status');
    const statusMessage = statusContainer.querySelector('.status-message');

    if (statusContainer && statusMessage) {
        statusMessage.textContent = message;
        statusContainer.className = `status-container status-${type}`;
        statusContainer.style.display = 'block';

        // 自动隐藏成功和错误消息
        if (type === 'success' || type === 'error') {
            setTimeout(() => {
                statusContainer.style.display = 'none';
            }, 5000);
        }
    }
}

/**
 * 显示CSDN进度条
 */
function showCSDNProgress(percentage) {
    const progressBar = document.getElementById('csdn-progress');
    const progressFill = progressBar.querySelector('.progress-fill');

    if (progressBar && progressFill) {
        progressFill.style.width = `${percentage}%`;
        progressBar.style.display = 'block';
    }
}

/**
 * 隐藏CSDN进度条
 */
function hideCSDNProgress() {
    const progressBar = document.getElementById('csdn-progress');
    if (progressBar) {
        setTimeout(() => {
            progressBar.style.display = 'none';
        }, 1000);
    }
}

/**
 * 清空CSDN解析结果
 */
function clearCSDNResults() {
    // 清空输入框
    const inputField = document.getElementById('csdn-url');
    if (inputField) {
        inputField.value = '';
    }

    // 隐藏结果容器
    const resultContainer = document.getElementById('csdn-result');
    if (resultContainer) {
        resultContainer.style.display = 'none';
    }

    // 清空文本内容
    const elements = [
        'csdn-article-title', 'csdn-article-author',
        'csdn-article-time', 'csdn-article-reads', 'csdn-article-content'
    ];
    elements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = '-';
        }
    });

    // 隐藏状态容器
    const statusContainer = document.getElementById('csdn-status');
    if (statusContainer) {
        statusContainer.style.display = 'none';
    }

    // 清空当前文章数据
    currentArticleData = null;
}

/**
 * 验证CSDN URL
 */
function isValidCSDNUrl(url) {
    try {
        const urlObj = new URL(url);
        return urlObj.hostname.includes('csdn.net') &&
               (urlObj.pathname.includes('/article/details/') || urlObj.pathname.includes('/blog/details/'));
    } catch {
        return false;
    }
}

/**
 * 验证邮箱格式
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * 记录下载
 */
function recordDownload(platform, title, url, fileType = 'unknown') {
    // 这里可以调用下载记录API
    console.log('记录下载:', { platform, title, url, fileType });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initCSDNFeatures();
});
