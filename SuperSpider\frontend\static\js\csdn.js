// CSDN文章解析功能
class CSDNParser {
    constructor() {
        this.apiUrl = '/api/csdn';
        this.isLoading = false;
        this.currentData = null;
        this.init();
    }

    init() {
        this.bindEvents();
    }

    bindEvents() {
        // 绑定解析按钮事件
        const parseBtn = document.getElementById('csdn-parse-btn');
        if (parseBtn) {
            parseBtn.addEventListener('click', () => this.parseArticle());
        }

        // 绑定输入框回车事件
        const urlInput = document.getElementById('csdn-url-input');
        if (urlInput) {
            urlInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.parseArticle();
                }
            });
        }
    }

    async parseArticle() {
        if (this.isLoading) return;

        const urlInput = document.getElementById('csdn-url-input');
        const url = urlInput?.value?.trim();

        if (!url) {
            this.showMessage('请输入CSDN文章链接', 'error');
            return;
        }

        if (!url.includes('blog.csdn.net')) {
            this.showMessage('请输入有效的CSDN文章链接', 'error');
            return;
        }

        this.setLoading(true);
        this.clearResults();

        try {
            const response = await fetch(`${this.apiUrl}/parse`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    article_url: url
                })
            });

            const data = await response.json();

            if (data.success) {
                this.currentData = data.data;
                this.displayResults(data.data);
                this.showMessage('解析成功！', 'success');
            } else {
                this.showMessage(data.message || '解析失败', 'error');
            }
        } catch (error) {
            console.error('解析失败:', error);
            this.showMessage('网络错误，请稍后重试', 'error');
        } finally {
            this.setLoading(false);
        }
    }

    displayResults(data) {
        const resultsContainer = document.getElementById('csdn-results');
        if (!resultsContainer) return;

        resultsContainer.innerHTML = `
            <div class="result-card">
                <div class="result-header">
                    <h3 class="result-title">${this.escapeHtml(data.title || '未知标题')}</h3>
                    <div class="result-meta">
                        <span class="author">作者: ${this.escapeHtml(data.author || '未知作者')}</span>
                        <span class="platform">平台: ${this.escapeHtml(data.platform || 'CSDN')}</span>
                        ${data.publish_time ? `<span class="time">发布时间: ${this.escapeHtml(data.publish_time)}</span>` : ''}
                    </div>
                </div>
                <div class="result-content">
                    <div class="content-preview">
                        <h4>内容预览:</h4>
                        <p>${this.escapeHtml(data.content_preview || data.content || '无内容预览')}</p>
                    </div>
                </div>
                <div class="result-actions">
                    <a href="${this.escapeHtml(data.url || '')}" target="_blank" class="btn btn-secondary">
                        <i class="fas fa-external-link-alt"></i> 查看原文
                    </a>
                </div>
            </div>
        `;

        resultsContainer.style.display = 'block';
    }

    clearResults() {
        const resultsContainer = document.getElementById('csdn-results');
        if (resultsContainer) {
            resultsContainer.innerHTML = '';
            resultsContainer.style.display = 'none';
        }
    }

    setLoading(loading) {
        this.isLoading = loading;
        const parseBtn = document.getElementById('csdn-parse-btn');
        const urlInput = document.getElementById('csdn-url-input');

        if (parseBtn) {
            parseBtn.disabled = loading;
            parseBtn.innerHTML = loading ? 
                '<i class="fas fa-spinner fa-spin"></i> 解析中...' : 
                '<i class="fas fa-search"></i> 解析文章';
        }

        if (urlInput) {
            urlInput.disabled = loading;
        }
    }

    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageDiv = document.createElement('div');
        messageDiv.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show`;
        messageDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // 找到消息容器
        let messageContainer = document.getElementById('csdn-messages');
        if (!messageContainer) {
            messageContainer = document.createElement('div');
            messageContainer.id = 'csdn-messages';
            messageContainer.className = 'message-container';
            
            const container = document.querySelector('.csdn-container') || document.body;
            container.insertBefore(messageContainer, container.firstChild);
        }

        // 添加消息
        messageContainer.appendChild(messageDiv);

        // 自动移除消息
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, 5000);
    }

    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    if (document.querySelector('.csdn-container')) {
        new CSDNParser();
    }
});

// 导出类供其他模块使用
window.CSDNParser = CSDNParser;
