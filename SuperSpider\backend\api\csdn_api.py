#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CSDN API模块
提供CSDN文章解析和下载相关的API
"""

import logging
import os
import traceback
from typing import Dict, Any, Optional

from flask import Blueprint, request, jsonify
from flask_login import login_required

from ..utils.mailer import QQMailer
from ..utils.settings import QQ_EMAIL, QQ_AUTH_CODE
from ..utils.permissions import require_permission
from ..spiders.csdn_spider import CSDNSpider
from ..utils.pdf_generator import create_pdf_generator
from ..utils.screenshot_generator import create_screenshot_generator

# 创建日志记录器
logger = logging.getLogger(__name__)

# 创建蓝图
csdn_api = Blueprint('csdn_api', __name__)

def create_email_service() -> Optional[QQMailer]:
    """
    创建邮件服务

    Returns:
        邮件服务实例，如果未配置则返回None
    """
    if QQ_EMAIL and QQ_AUTH_CODE:
        return QQMailer(QQ_EMAIL, QQ_AUTH_CODE)
    return None

@csdn_api.route('/parse', methods=['POST'])
@require_permission('platform', 'csdn')
def parse_article():
    """
    解析CSDN文章链接

    请求体:
        {
            "article_url": "文章URL"
        }

    响应:
        {
            "success": true,
            "message": "解析成功",
            "data": {
                "title": "文章标题",
                "author": "作者名字",
                "content": "文章内容"
            }
        }
    """
    try:
        # 验证请求参数
        if not request.is_json:
            return jsonify({
                "success": False,
                "message": "请求格式错误，需要JSON格式",
                "data": None
            }), 400

        data = request.json
        article_url = data.get('article_url')

        # 验证必要参数
        if not article_url:
            return jsonify({
                "success": False,
                "message": "请提供文章URL",
                "data": None
            }), 400

        logger.info(f"开始解析CSDN文章: {article_url}")

        # 创建CSDN爬虫实例
        spider = CSDNSpider()

        # 执行解析
        result = spider.execute({"article_url": article_url})

        if result.get("success"):
            article_data = result.get("data", {})

            # 格式化内容用于前端显示
            if article_data.get("content"):
                article_data["content_preview"] = spider.format_content_for_display(
                    article_data["content"], max_length=500
                )

            logger.info(f"成功解析CSDN文章: {article_data.get('title', '未知标题')}")

            return jsonify({
                "success": True,
                "message": "解析成功",
                "data": article_data
            }), 200
        else:
            return jsonify({
                "success": False,
                "message": "解析失败",
                "data": None
            }), 400

    except Exception as e:
        logger.error(f"解析文章失败: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "message": f"服务器错误: {str(e)}",
            "data": None
        }), 500

@csdn_api.route('/download', methods=['POST'])
@require_permission('platform', 'csdn')
def download_article():
    """
    下载CSDN文章并发送到邮箱

    请求体:
        {
            "article_url": "文章URL",
            "email": "接收邮箱地址"
        }

    响应:
        {
            "success": true,
            "message": "下载完成，已发送至邮箱",
            "data": {
                "title": "文章标题",
                "author": "作者名字",
                "file_path": "文件路径"
            }
        }
    """
    try:
        # 验证请求参数
        if not request.is_json:
            return jsonify({
                "success": False,
                "message": "请求格式错误，需要JSON格式",
                "data": None
            }), 400

        data = request.json
        article_url = data.get('article_url')
        email = data.get('email')

        # 验证必要参数
        if not article_url:
            return jsonify({
                "success": False,
                "message": "请提供文章URL",
                "data": None
            }), 400

        if not email:
            return jsonify({
                "success": False,
                "message": "请提供接收邮箱地址",
                "data": None
            }), 400

        logger.info(f"开始下载CSDN文章: {article_url}")

        # 创建CSDN爬虫实例
        spider = CSDNSpider()

        # 执行解析
        result = spider.execute({"article_url": article_url})

        if not result.get("success"):
            return jsonify({
                "success": False,
                "message": "文章解析失败",
                "data": None
            }), 400

        article_data = result.get("data", {})

        try:
            # 创建PDF生成器
            downloads_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'downloads')
            pdf_generator = create_pdf_generator(downloads_dir)

            # 生成PDF文件
            file_path = pdf_generator.generate_article_pdf(article_data)

            # 发送邮件
            mailer = create_email_service()
            if mailer and email:
                try:
                    subject = f"CSDN文章下载: {article_data.get('title', '未知标题')}"
                    body = f"""
您好！

您请求下载的CSDN文章已经准备完成：

标题: {article_data.get('title', '')}
作者: {article_data.get('author', '')}
原文链接: {article_data.get('url', '')}

请查看附件中的文档。

此邮件由SuperSpider自动发送。
                    """

                    mailer.send_email(
                        to_email=email,
                        subject=subject,
                        body=body,
                        attachment_path=file_path
                    )

                    logger.info(f"文章已发送至邮箱: {email}")
                    message = "下载完成，已发送至邮箱"

                except Exception as e:
                    logger.error(f"发送邮件失败: {e}")
                    message = "文件生成成功，但邮件发送失败"
            else:
                message = "文件生成成功，邮件服务未配置"

            return jsonify({
                "success": True,
                "message": message,
                "data": {
                    "title": article_data.get('title', ''),
                    "author": article_data.get('author', ''),
                    "file_path": os.path.basename(file_path),
                    "file_size": os.path.getsize(file_path) if os.path.exists(file_path) else 0
                }
            }), 200

        except Exception as e:
            logger.error(f"生成文件失败: {e}")
            return jsonify({
                "success": False,
                "message": f"生成文件失败: {str(e)}",
                "data": None
            }), 500

    except Exception as e:
        logger.error(f"下载文章失败: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "message": f"服务器错误: {str(e)}",
            "data": None
        }), 500

@csdn_api.route('/screenshot', methods=['POST'])
@login_required
@require_permission('pro')
def screenshot_article():
    """
    生成CSDN文章截图并发送到邮箱
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "message": "请求数据不能为空",
                "data": None
            }), 400

        article_url = data.get('article_url', '').strip()
        email = data.get('email', '').strip()

        if not article_url:
            return jsonify({
                "success": False,
                "message": "文章链接不能为空",
                "data": None
            }), 400

        if not email:
            return jsonify({
                "success": False,
                "message": "邮箱地址不能为空",
                "data": None
            }), 400

        logger.info(f"开始生成CSDN文章截图: {article_url}")

        # 创建CSDN爬虫实例获取文章标题
        spider = CSDNSpider()
        result = spider.execute({"article_url": article_url})

        article_title = "CSDN文章"
        if result.get("success"):
            article_data = result.get("data", {})
            article_title = article_data.get('title', 'CSDN文章')

        try:
            # 创建截图生成器
            downloads_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'downloads')
            screenshot_generator = create_screenshot_generator(downloads_dir)

            # 生成截图文件
            file_path = screenshot_generator.generate_article_screenshot(article_url, article_title)

            # 发送邮件
            mailer = create_email_service()
            if mailer and email:
                try:
                    subject = f"CSDN文章截图: {article_title}"
                    body = f"""
您好！

您请求的CSDN文章截图已经准备完成：

标题: {article_title}
原文链接: {article_url}

请查看附件中的截图文件。

此邮件由SuperSpider自动发送。
                    """

                    mailer.send_email(
                        to_email=email,
                        subject=subject,
                        body=body,
                        attachment_path=file_path
                    )

                    logger.info(f"截图已发送至邮箱: {email}")
                    message = "截图生成完成，已发送至邮箱"

                except Exception as e:
                    logger.error(f"发送邮件失败: {e}")
                    message = "截图生成成功，但邮件发送失败"
            else:
                message = "截图生成成功，邮件服务未配置"

            return jsonify({
                "success": True,
                "message": message,
                "data": {
                    "title": article_title,
                    "file_path": os.path.basename(file_path),
                    "file_size": os.path.getsize(file_path) if os.path.exists(file_path) else 0,
                    "file_type": "screenshot"
                }
            }), 200

        except Exception as e:
            logger.error(f"生成截图失败: {e}")
            return jsonify({
                "success": False,
                "message": f"生成截图失败: {str(e)}",
                "data": None
            }), 500

    except Exception as e:
        logger.error(f"截图请求处理失败: {e}")
        return jsonify({
            "success": False,
            "message": "服务器内部错误",
            "data": None
        }), 500

@csdn_api.route('/status', methods=['GET'])
def check_status():
    """
    检查CSDN API服务状态

    响应:
        {
            "success": true,
            "message": "服务正常",
            "data": {
                "status": "ready",
                "email": {
                    "available": true
                }
            }
        }
    """
    try:
        # 检查邮件服务
        mailer = create_email_service()
        email_available = mailer is not None

        return jsonify({
            "success": True,
            "message": "服务正常",
            "data": {
                "status": "ready",
                "email": {
                    "available": email_available
                }
            }
        }), 200

    except Exception as e:
        logger.error(f"检查状态失败: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "message": f"服务异常: {str(e)}",
            "data": None
        }), 500

# 添加GET方法的解析接口
@csdn_api.route('/parse', methods=['GET'])
@require_permission('platform', 'csdn')
def parse_article_get():
    """
    解析CSDN文章链接（GET方法）

    查询参数:
        article_url: 文章URL

    响应:
        {
            "success": true,
            "message": "解析成功",
            "data": {
                "title": "文章标题",
                "author": "作者名字",
                "content": "文章内容"
            }
        }
    """
    try:
        article_url = request.args.get('article_url')

        # 验证必要参数
        if not article_url:
            return jsonify({
                "success": False,
                "message": "请提供文章URL",
                "data": None
            }), 400

        logger.info(f"开始解析CSDN文章: {article_url}")

        # 创建CSDN爬虫实例
        spider = CSDNSpider()

        # 执行解析
        result = spider.execute({"article_url": article_url})

        if result.get("success"):
            article_data = result.get("data", {})

            # 格式化内容用于前端显示
            if article_data.get("content"):
                article_data["content_preview"] = spider.format_content_for_display(
                    article_data["content"], max_length=500
                )

            logger.info(f"成功解析CSDN文章: {article_data.get('title', '未知标题')}")

            return jsonify({
                "success": True,
                "message": "解析成功",
                "data": article_data
            }), 200
        else:
            return jsonify({
                "success": False,
                "message": "解析失败",
                "data": None
            }), 400

    except Exception as e:
        logger.error(f"解析文章失败: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "message": f"服务器错误: {str(e)}",
            "data": None
        }), 500