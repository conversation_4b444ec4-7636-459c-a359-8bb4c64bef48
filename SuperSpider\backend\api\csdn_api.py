#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CSDN API模块
提供CSDN文章解析功能
"""

import logging
from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user
from ..spiders.csdn_spider import <PERSON><PERSON><PERSON>pider
from ..utils.permissions import require_permission

logger = logging.getLogger(__name__)

# 创建蓝图
csdn_api = Blueprint('csdn_api', __name__)

# 初始化爬虫
spider = CSDNSpider()


@csdn_api.route('/parse', methods=['POST'])
@login_required
@require_permission('platform', 'csdn')
def parse_article():
    """解析CSDN文章"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "success": False,
                "message": "请提供有效的JSON数据",
                "data": None
            }), 400

        article_url = data.get('article_url', '').strip()
        if not article_url:
            return jsonify({
                "success": False,
                "message": "请提供文章URL",
                "data": None
            }), 400

        # 验证是否为CSDN链接
        if 'blog.csdn.net' not in article_url:
            return jsonify({
                "success": False,
                "message": "请提供有效的CSDN文章链接",
                "data": None
            }), 400

        logger.info(f"用户 {current_user.username} 开始解析CSDN文章: {article_url}")

        # 执行解析
        result = spider.execute({"article_url": article_url})

        if result["success"]:
            # 格式化内容用于前端显示
            if result["data"] and result["data"].get("content"):
                result["data"]["content_preview"] = spider.format_content_for_display(
                    result["data"]["content"], 500
                )

            logger.info(f"用户 {current_user.username} 成功解析CSDN文章: {result['data'].get('title', '未知标题')}")
        else:
            logger.warning(f"用户 {current_user.username} 解析CSDN文章失败: {result['message']}")

        return jsonify(result)

    except Exception as e:
        logger.error(f"解析CSDN文章时发生错误: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"服务器错误: {str(e)}",
            "data": None
        }), 500


@csdn_api.route('/status', methods=['GET'])
def check_status():
    """检查CSDN API状态"""
    try:
        status = spider.check_status()
        return jsonify({
            "success": True,
            "message": "CSDN API运行正常",
            "data": status
        })
    except Exception as e:
        logger.error(f"检查CSDN状态失败: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"状态检查失败: {str(e)}",
            "data": None
        }), 500


# 错误处理
@csdn_api.errorhandler(404)
def not_found(error):
    """处理404错误"""
    return jsonify({
        "success": False,
        "message": "API接口不存在",
        "data": None
    }), 404


@csdn_api.errorhandler(405)
def method_not_allowed(error):
    """处理405错误"""
    return jsonify({
        "success": False,
        "message": "请求方法不允许",
        "data": None
    }), 405


@csdn_api.errorhandler(500)
def internal_error(error):
    """处理500错误"""
    return jsonify({
        "success": False,
        "message": "服务器内部错误",
        "data": None
    }), 500