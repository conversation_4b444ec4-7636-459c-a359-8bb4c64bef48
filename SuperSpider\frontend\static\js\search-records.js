/**
 * SuperSpider 搜索历史记录脚本
 * 处理用户搜索历史记录相关功能
 */

// 当前页码
let currentPage = 1;
// 每页记录数
const perPage = 10;
// 当前筛选条件
let currentFilters = {};
// 选中的搜索记录ID
let selectedSearchIds = [];
// 当前视图模式：'list' 或 'grid'
let currentViewMode = 'list';
// 当前排序方式
let currentSort = { field: 'created_at', order: 'desc' };

// 防抖变量
let loadHistoryTimeout = null;
let loadStatsTimeout = null;

// 初始化搜索历史记录功能
function initSearchFeatures() {
    // 搜索记录链接点击事件
    const searchLink = document.getElementById('downloads-link');
    if (searchLink) {
        // 移除可能存在的旧事件监听器
        searchLink.removeEventListener('click', handleSearchLinkClick);
        // 添加新的事件监听器
        searchLink.addEventListener('click', handleSearchLinkClick);
    }
}

// 处理搜索记录链接点击事件
function handleSearchLinkClick(e) {
    e.preventDefault();
    openModal('downloads-modal');
    loadSearchHistory();
}

// 防抖版本的加载搜索历史记录
function loadSearchHistoryDebounced(delay = 300) {
    if (loadHistoryTimeout) {
        clearTimeout(loadHistoryTimeout);
    }

    loadHistoryTimeout = setTimeout(() => {
        loadSearchHistory();
    }, delay);
}

// 防抖版本的加载搜索统计
function loadSearchStatsDebounced(delay = 300) {
    if (loadStatsTimeout) {
        clearTimeout(loadStatsTimeout);
    }

    loadStatsTimeout = setTimeout(() => {
        loadSearchStats();
    }, delay);
}

// 加载搜索历史记录
async function loadSearchHistory() {
    const searchList = document.getElementById('downloads-list');
    const searchPagination = document.getElementById('downloads-pagination');
    const searchLoading = document.getElementById('downloads-loading');
    const searchEmpty = document.getElementById('downloads-empty');

    if (!searchList || !searchPagination) return;

    try {
        // 显示加载中
        if (searchLoading) {
            searchLoading.style.display = 'block';
        }

        // 隐藏空状态
        if (searchEmpty) {
            searchEmpty.style.display = 'none';
        }

        // 构建查询参数
        const params = new URLSearchParams({
            page: currentPage,
            per_page: perPage
        });

        // 添加筛选条件
        Object.entries(currentFilters).forEach(([key, value]) => {
            if (value !== undefined) {
                params.append(key, value);
            }
        });

        // 发送请求
        let url = '/api/search/history';
        if (currentFilters.is_favorite) {
            url = '/api/search/favorites';
        }

        const response = await fetch(`${url}?${params.toString()}`);
        const data = await response.json();

        // 隐藏加载中
        if (searchLoading) {
            searchLoading.style.display = 'none';
        }

        if (data.success) {
            const { records, total, page, pages } = data.data;

            // 保存搜索记录，用于视图切换
            window.lastSearches = records;

            // 更新当前页码
            currentPage = page;

            // 检查是否有数据
            if (records.length === 0) {
                // 显示空状态
                if (searchEmpty) {
                    searchEmpty.style.display = 'block';
                }

                // 清空列表和分页
                searchList.innerHTML = '';
                searchPagination.innerHTML = '';
                return;
            }

            // 渲染搜索列表
            renderSearchList(records);

            // 渲染分页
            renderPagination(page, pages, total);

            // 只在首次加载时加载搜索统计，避免重复请求
            if (currentPage === 1 && Object.keys(currentFilters).length === 0) {
                loadSearchStats();
            }

            // 重置选中的搜索记录ID
            selectedSearchIds = [];

            // 更新批量操作按钮状态
            updateBatchActionButtons();
        } else {
            console.error('获取搜索历史记录失败:', data.message);

            // 显示错误消息
            searchList.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    <p>获取搜索历史记录失败: ${data.message}</p>
                </div>
            `;

            // 清空分页
            searchPagination.innerHTML = '';
        }
    } catch (error) {
        console.error('获取搜索历史记录请求失败:', error);

        // 隐藏加载中
        if (searchLoading) {
            searchLoading.style.display = 'none';
        }

        // 显示错误消息
        searchList.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-circle"></i>
                <p>获取搜索历史记录请求失败，请稍后再试</p>
            </div>
        `;

        // 清空分页
        searchPagination.innerHTML = '';
    }
}

// 加载搜索统计
async function loadSearchStats() {
    try {
        const response = await fetch('/api/search/stats');
        const data = await response.json();

        if (data.success) {
            const stats = data.data;
            
            // 更新统计显示
            const statsContainer = document.getElementById('download-stats');
            if (statsContainer) {
                const statItems = statsContainer.querySelectorAll('.stat-item .stat-value');
                if (statItems.length >= 3) {
                    statItems[0].textContent = stats.total_searches || 0;
                    statItems[1].textContent = stats.recent_searches || 0;
                    statItems[2].textContent = stats.favorite_count || 0;
                }
            }
        }
    } catch (error) {
        console.error('获取搜索统计失败:', error);
    }
}

// 渲染搜索列表
function renderSearchList(records) {
    const searchList = document.getElementById('downloads-list');
    if (!searchList) return;

    // 清空列表
    searchList.innerHTML = '';

    // 根据视图模式渲染
    if (currentViewMode === 'grid') {
        renderGridView(records, searchList);
    } else {
        renderListView(records, searchList);
    }
}

// 渲染列表视图
function renderListView(records, container) {
    // 创建表格
    const table = document.createElement('table');
    table.className = 'downloads-table';

    // 创建表头
    const thead = document.createElement('thead');
    thead.innerHTML = `
        <tr>
            <th class="checkbox-column">
                <input type="checkbox" id="select-all-checkbox">
            </th>
            <th>标题</th>
            <th>平台</th>
            <th>类型</th>
            <th>状态</th>
            <th>时间</th>
            <th>操作</th>
        </tr>
    `;
    table.appendChild(thead);

    // 创建表体
    const tbody = document.createElement('tbody');

    records.forEach(record => {
        const tr = document.createElement('tr');

        // 设置状态类名
        if (record.status === 'success') {
            tr.classList.add('status-completed');
        } else if (record.status === 'failed') {
            tr.classList.add('status-failed');
        }

        // 如果是收藏，添加收藏类名
        if (record.is_favorite) {
            tr.classList.add('is-favorite');
        }

        // 设置行内容
        tr.innerHTML = `
            <td class="checkbox-column">
                <input type="checkbox" class="search-checkbox" data-id="${record.id}">
            </td>
            <td class="search-title" title="${record.title || '未知标题'}">${record.title || '未知标题'}</td>
            <td>${getPlatformLabel(record.platform)}</td>
            <td>${getContentTypeLabel(record.content_type)}</td>
            <td>${getStatusLabel(record.status)}</td>
            <td>${formatDate(record.created_at)}</td>
            <td>
                <div class="search-actions">
                    ${record.video_url ? 
                        `<a href="${record.video_url}" target="_blank" class="view-btn" title="查看视频">
                            <i class="fas fa-external-link-alt"></i>
                        </a>` : ''
                    }
                    <button class="action-btn favorite-btn ${record.is_favorite ? 'active' : ''}"
                            data-id="${record.id}"
                            title="${record.is_favorite ? '取消收藏' : '收藏'}"
                            onclick="toggleFavorite(${record.id})">
                        <i class="fas fa-star"></i>
                    </button>
                    <button class="action-btn notes-btn ${record.notes ? 'has-notes' : ''}"
                            data-id="${record.id}"
                            title="${record.notes ? '编辑笔记' : '添加笔记'}"
                            onclick="showNotesModal(${record.id}, '${record.title || '未知标题'}')">
                        <i class="fas fa-sticky-note"></i>
                    </button>
                    <button class="action-btn delete-btn"
                            data-id="${record.id}"
                            title="删除"
                            onclick="deleteSearchRecord(${record.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;

        tbody.appendChild(tr);
    });

    table.appendChild(tbody);
    container.appendChild(table);

    // 添加复选框事件
    addCheckboxEvents();
}

// 页面加载完成后初始化搜索历史记录功能
document.addEventListener('DOMContentLoaded', () => {
    initSearchFeatures();
});
