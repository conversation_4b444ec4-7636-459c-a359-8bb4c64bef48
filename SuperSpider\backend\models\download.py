#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
下载历史记录模型
跟踪用户的下载历史
"""

import datetime
from .user import db

class Download(db.Model):
    """下载历史记录模型类"""
    __tablename__ = 'downloads'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    platform = db.Column(db.String(50), nullable=False, index=True)  # 平台名称，如 'kuaishou', 'csdn'
    content_type = db.Column(db.String(50), nullable=False)  # 内容类型，如 'video', 'article'
    content_url = db.Column(db.String(1024), nullable=False)  # 原始内容URL
    title = db.Column(db.String(255))  # 内容标题
    author = db.Column(db.String(100))  # 内容作者
    file_path = db.Column(db.String(1024))  # 下载文件路径
    file_size = db.Column(db.Integer)  # 文件大小（字节）
    status = db.Column(db.String(20), default='completed', nullable=False)  # 下载状态：pending, completed, failed
    created_at = db.Column(db.DateTime, default=datetime.datetime.now, nullable=False)

    # 新增字段
    download_count = db.Column(db.Integer, default=0)  # 下载次数
    last_downloaded_at = db.Column(db.DateTime)  # 最后下载时间
    thumbnail_url = db.Column(db.String(1024))  # 缩略图URL
    duration = db.Column(db.Integer)  # 视频时长（秒）
    file_format = db.Column(db.String(20))  # 文件格式，如 'mp4', 'mp3', 'pdf'
    resolution = db.Column(db.String(20))  # 视频分辨率，如 '720p', '1080p'
    is_favorite = db.Column(db.Boolean, default=False)  # 是否收藏
    notes = db.Column(db.Text)  # 用户笔记

    # 关联到用户模型
    user = db.relationship('User', backref=db.backref('downloads', lazy='dynamic'))

    def __init__(self, user_id, platform, content_type, content_url, **kwargs):
        """初始化下载记录"""
        self.user_id = user_id
        self.platform = platform
        self.content_type = content_type
        self.content_url = content_url

        for key, value in kwargs.items():
            setattr(self, key, value)

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'platform': self.platform,
            'content_type': self.content_type,
            'content_url': self.content_url,
            'title': self.title,
            'author': self.author,
            'file_path': self.file_path,
            'file_size': self.file_size,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'download_count': self.download_count,
            'last_downloaded_at': self.last_downloaded_at.isoformat() if self.last_downloaded_at else None,
            'thumbnail_url': self.thumbnail_url,
            'duration': self.duration,
            'file_format': self.file_format,
            'resolution': self.resolution,
            'is_favorite': self.is_favorite,
            'notes': self.notes
        }

    def increment_download_count(self):
        """增加下载次数"""
        self.download_count += 1
        self.last_downloaded_at = datetime.datetime.now()

    def toggle_favorite(self):
        """切换收藏状态"""
        self.is_favorite = not self.is_favorite
        return self.is_favorite

    def update_notes(self, notes):
        """更新笔记"""
        self.notes = notes

    def __repr__(self):
        """字符串表示"""
        return f'<Download {self.id}: {self.platform} - {self.title}>'
