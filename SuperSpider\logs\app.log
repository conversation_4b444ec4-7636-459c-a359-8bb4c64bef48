2025-05-27 16:15:36,698 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:15:36,699 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:15:36,700 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:15:36,700 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:15:36,700 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:15:37,211 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:15:38,181 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:15:38,185 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:15:38,209 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:15:38,216 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:15:38,224 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:15:38,228 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:15:38,231 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:15:38,236 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:15:38,276 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 16:15:38,371 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 16:15:38,374 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 16:15:38,379 - werkzeug - INFO -  * Restarting with stat
2025-05-27 16:15:39,210 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:15:39,210 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:15:39,210 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:15:39,210 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:15:39,212 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:15:39,693 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:15:40,161 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:15:40,164 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:15:40,179 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:15:40,193 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:15:40,198 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:15:40,203 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:15:40,211 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:15:40,214 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:15:40,257 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 16:15:40,277 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 16:15:40,298 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 16:15:50,421 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET / HTTP/1.1" 200 -
2025-05-27 16:15:50,643 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/css/style.css HTTP/1.1" 200 -
2025-05-27 16:15:50,644 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/css/user.css HTTP/1.1" 200 -
2025-05-27 16:15:50,688 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/permissions.js HTTP/1.1" 200 -
2025-05-27 16:15:50,703 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/images/wechat-qrcode.jpg HTTP/1.1" 200 -
2025-05-27 16:15:50,768 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/css/downloads.css HTTP/1.1" 200 -
2025-05-27 16:15:50,790 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/validation.js HTTP/1.1" 200 -
2025-05-27 16:15:50,830 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/auth.js HTTP/1.1" 200 -
2025-05-27 16:15:50,853 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-27 16:15:50,858 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/downloads.js HTTP/1.1" 200 -
2025-05-27 16:15:50,860 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/video-controls.js HTTP/1.1" 200 -
2025-05-27 16:15:50,867 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/permission-management.js HTTP/1.1" 200 -
2025-05-27 16:15:50,869 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/direct-auth.js HTTP/1.1" 200 -
2025-05-27 16:15:51,171 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 16:15:51,201 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 16:15:51,669 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 16:15:51,683 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 16:15:51,759 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 16:18:33,552 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-27 16:18:34,726 - werkzeug - INFO -  * Restarting with stat
2025-05-27 16:18:37,875 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:18:37,889 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:18:37,894 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:18:37,895 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:18:37,878 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:18:40,141 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:18:41,155 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:18:41,160 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:18:41,223 - superspider - ERROR - 注册CSDN API路由失败: No module named 'bs4'
2025-05-27 16:18:41,235 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:18:41,243 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:18:41,254 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:18:41,259 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:18:41,269 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:18:41,307 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 16:18:41,354 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 16:18:41,392 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 16:18:56,736 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-27 16:18:57,125 - werkzeug - INFO -  * Restarting with stat
2025-05-27 16:18:58,081 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:18:58,082 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:18:58,083 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:18:58,083 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:18:58,084 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:18:58,601 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:18:59,272 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:18:59,278 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:18:59,298 - superspider - ERROR - 注册CSDN API路由失败: No module named 'bs4'
2025-05-27 16:18:59,307 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:18:59,323 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:18:59,326 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:18:59,331 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:18:59,340 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:18:59,384 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 16:18:59,443 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 16:18:59,463 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 16:19:14,713 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-27 16:19:15,008 - werkzeug - INFO -  * Restarting with stat
2025-05-27 16:19:15,942 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:19:15,943 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:19:15,943 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:19:15,947 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:19:15,947 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:19:16,347 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:19:16,864 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:19:16,868 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:19:16,882 - superspider - ERROR - 注册CSDN API路由失败: No module named 'bs4'
2025-05-27 16:19:16,887 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:19:16,891 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:19:16,897 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:19:16,902 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:19:16,905 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:19:16,940 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 16:19:16,971 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 16:19:16,988 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 16:20:52,460 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:20:52] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 16:20:58,135 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-27 16:20:58,795 - werkzeug - INFO -  * Restarting with stat
2025-05-27 16:21:00,379 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:21:00,384 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:21:00,384 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:21:00,385 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:21:00,385 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:21:00,960 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:21:01,953 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:21:01,966 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:21:02,002 - superspider - ERROR - 注册CSDN API路由失败: No module named 'bs4'
2025-05-27 16:21:02,015 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:21:02,026 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:21:02,035 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:21:02,049 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:21:02,056 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:21:02,101 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 16:21:02,179 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 16:21:02,235 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 16:21:27,760 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-27 16:21:28,166 - werkzeug - INFO -  * Restarting with stat
2025-05-27 16:21:29,098 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:21:29,100 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:21:29,101 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:21:29,102 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:21:29,103 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:21:29,569 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:21:30,134 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:21:30,138 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:21:30,159 - superspider - ERROR - 注册CSDN API路由失败: No module named 'bs4'
2025-05-27 16:21:30,166 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:21:30,173 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:21:30,181 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:21:30,185 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:21:30,193 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:21:30,215 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 16:21:30,254 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 16:21:30,274 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 16:45:58,587 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:45:58,590 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:45:58,590 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:45:58,590 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:45:58,590 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:45:58,639 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:45:58,640 - backend.utils.scheduler - WARNING - 调度器已在运行
2025-05-27 16:45:58,640 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:45:58,640 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:45:59,130 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:45:59,131 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:46:00,010 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:46:00,013 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:46:00,023 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:46:00,025 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:46:00,203 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:46:00,204 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:46:00,210 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:46:00,213 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:46:00,221 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:46:00,225 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:46:00,229 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:46:00,231 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:46:00,237 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:46:00,239 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:46:00,244 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:48:40,862 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:48:40,863 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:48:40,863 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:48:40,863 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:48:40,863 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:48:40,873 - backend.utils.scheduler - ERROR - 清理过期用户任务失败: 'NoneType' object has no attribute 'app_context'
2025-05-27 16:48:40,873 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 任务失败: 'NoneType' object has no attribute 'app_context'
2025-05-27 16:48:41,382 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:48:42,178 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:48:42,182 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:48:42,366 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:48:42,375 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:48:42,379 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:48:42,382 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:48:42,385 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:48:42,389 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:48:42,441 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 16:48:42,442 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 16:48:42,443 - werkzeug - INFO -  * Restarting with stat
2025-05-27 16:48:43,161 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:48:43,162 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:48:43,162 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:48:43,162 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:48:43,162 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:48:43,164 - backend.utils.scheduler - ERROR - 清理过期用户任务失败: 'NoneType' object has no attribute 'app_context'
2025-05-27 16:48:43,164 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 任务失败: 'NoneType' object has no attribute 'app_context'
2025-05-27 16:48:43,565 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:48:43,993 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:48:43,996 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:48:44,134 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:48:44,143 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:48:44,147 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:48:44,150 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:48:44,156 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:48:44,158 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:48:44,183 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 16:48:44,201 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 16:51:40,895 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:51:40,900 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:51:40,900 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:51:40,900 - backend.utils.scheduler - ERROR - 清理过期用户任务失败: 'NoneType' object has no attribute 'app_context'
2025-05-27 16:51:40,900 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:51:40,900 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 任务失败: 'NoneType' object has no attribute 'app_context'
2025-05-27 16:51:40,900 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:51:41,466 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:51:42,168 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:51:42,172 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:51:42,345 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:51:42,350 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:51:42,355 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:51:42,357 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:51:42,360 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:51:42,365 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:51:42,423 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 16:51:42,424 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 16:51:42,426 - werkzeug - INFO -  * Restarting with stat
2025-05-27 16:51:43,110 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:51:43,113 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:51:43,113 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:51:43,113 - backend.utils.scheduler - ERROR - 清理过期用户任务失败: 'NoneType' object has no attribute 'app_context'
2025-05-27 16:51:43,114 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:51:43,115 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 任务失败: 'NoneType' object has no attribute 'app_context'
2025-05-27 16:51:43,115 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:51:43,495 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:51:43,940 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:51:43,942 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:51:44,076 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:51:44,083 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:51:44,088 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:51:44,091 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:51:44,093 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:51:44,098 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:51:44,126 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 16:51:44,143 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 16:52:25,387 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:25] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 16:52:25,488 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:25] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 16:52:25,618 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:25] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 16:52:26,158 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "GET / HTTP/1.1" 200 -
2025-05-27 16:52:26,228 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 16:52:26,299 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 16:52:26,305 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 16:52:26,322 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 16:52:26,340 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 16:52:26,341 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 16:52:26,369 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 16:52:26,414 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 16:52:26,439 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 16:52:26,456 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 16:52:26,491 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 16:52:26,692 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "GET /static/js/csdn.js HTTP/1.1" 200 -
2025-05-27 16:52:26,699 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 16:52:26,740 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 16:52:26,784 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 16:52:27,229 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:27] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 16:52:27,241 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:27] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 16:52:27,294 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:27] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 16:52:53,815 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:52:53,816 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:52:53,817 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:52:53,817 - backend.utils.scheduler - ERROR - 清理过期用户任务失败: 'NoneType' object has no attribute 'app_context'
2025-05-27 16:52:53,817 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:52:53,817 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 任务失败: 'NoneType' object has no attribute 'app_context'
2025-05-27 16:52:53,817 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:52:54,563 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:52:55,149 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:52:55,152 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:52:55,305 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:52:55,315 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:52:55,323 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:52:55,330 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:52:55,334 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:52:55,337 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:52:55,415 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 16:52:55,416 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 16:52:55,418 - werkzeug - INFO -  * Restarting with stat
2025-05-27 16:52:56,250 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:52:56,251 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:52:56,251 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:52:56,252 - backend.utils.scheduler - ERROR - 清理过期用户任务失败: 'NoneType' object has no attribute 'app_context'
2025-05-27 16:52:56,252 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:52:56,252 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 任务失败: 'NoneType' object has no attribute 'app_context'
2025-05-27 16:52:56,252 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:52:56,661 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:52:57,121 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:52:57,125 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:52:57,302 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:52:57,307 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:52:57,317 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:52:57,322 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:52:57,330 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:52:57,334 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:52:57,375 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 16:52:57,398 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 16:53:36,535 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:53:36] "GET /api/status HTTP/1.1" 200 -
2025-05-27 16:57:27,539 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:57:27] "GET /api/permission/check HTTP/1.1" 200 -
