2025-05-27 16:15:36,698 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:15:36,699 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:15:36,700 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:15:36,700 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:15:36,700 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:15:37,211 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:15:38,181 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:15:38,185 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:15:38,209 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:15:38,216 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:15:38,224 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:15:38,228 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:15:38,231 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:15:38,236 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:15:38,276 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 16:15:38,371 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 16:15:38,374 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 16:15:38,379 - werkzeug - INFO -  * Restarting with stat
2025-05-27 16:15:39,210 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:15:39,210 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:15:39,210 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:15:39,210 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:15:39,212 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:15:39,693 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:15:40,161 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:15:40,164 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:15:40,179 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:15:40,193 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:15:40,198 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:15:40,203 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:15:40,211 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:15:40,214 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:15:40,257 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 16:15:40,277 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 16:15:40,298 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 16:15:50,421 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET / HTTP/1.1" 200 -
2025-05-27 16:15:50,643 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/css/style.css HTTP/1.1" 200 -
2025-05-27 16:15:50,644 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/css/user.css HTTP/1.1" 200 -
2025-05-27 16:15:50,688 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/permissions.js HTTP/1.1" 200 -
2025-05-27 16:15:50,703 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/images/wechat-qrcode.jpg HTTP/1.1" 200 -
2025-05-27 16:15:50,768 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/css/downloads.css HTTP/1.1" 200 -
2025-05-27 16:15:50,790 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/validation.js HTTP/1.1" 200 -
2025-05-27 16:15:50,830 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/auth.js HTTP/1.1" 200 -
2025-05-27 16:15:50,853 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-27 16:15:50,858 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/downloads.js HTTP/1.1" 200 -
2025-05-27 16:15:50,860 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/video-controls.js HTTP/1.1" 200 -
2025-05-27 16:15:50,867 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/permission-management.js HTTP/1.1" 200 -
2025-05-27 16:15:50,869 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/direct-auth.js HTTP/1.1" 200 -
2025-05-27 16:15:51,171 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 16:15:51,201 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 16:15:51,669 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 16:15:51,683 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 16:15:51,759 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 16:18:33,552 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-27 16:18:34,726 - werkzeug - INFO -  * Restarting with stat
2025-05-27 16:18:37,875 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:18:37,889 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:18:37,894 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:18:37,895 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:18:37,878 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:18:40,141 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:18:41,155 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:18:41,160 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:18:41,223 - superspider - ERROR - 注册CSDN API路由失败: No module named 'bs4'
2025-05-27 16:18:41,235 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:18:41,243 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:18:41,254 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:18:41,259 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:18:41,269 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:18:41,307 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 16:18:41,354 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 16:18:41,392 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 16:18:56,736 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-27 16:18:57,125 - werkzeug - INFO -  * Restarting with stat
2025-05-27 16:18:58,081 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:18:58,082 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:18:58,083 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:18:58,083 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:18:58,084 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:18:58,601 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:18:59,272 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:18:59,278 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:18:59,298 - superspider - ERROR - 注册CSDN API路由失败: No module named 'bs4'
2025-05-27 16:18:59,307 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:18:59,323 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:18:59,326 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:18:59,331 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:18:59,340 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:18:59,384 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 16:18:59,443 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 16:18:59,463 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 16:19:14,713 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-27 16:19:15,008 - werkzeug - INFO -  * Restarting with stat
2025-05-27 16:19:15,942 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:19:15,943 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:19:15,943 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:19:15,947 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:19:15,947 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:19:16,347 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:19:16,864 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:19:16,868 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:19:16,882 - superspider - ERROR - 注册CSDN API路由失败: No module named 'bs4'
2025-05-27 16:19:16,887 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:19:16,891 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:19:16,897 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:19:16,902 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:19:16,905 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:19:16,940 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 16:19:16,971 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 16:19:16,988 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 16:20:52,460 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:20:52] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 16:20:58,135 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-27 16:20:58,795 - werkzeug - INFO -  * Restarting with stat
2025-05-27 16:21:00,379 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:21:00,384 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:21:00,384 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:21:00,385 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:21:00,385 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:21:00,960 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:21:01,953 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:21:01,966 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:21:02,002 - superspider - ERROR - 注册CSDN API路由失败: No module named 'bs4'
2025-05-27 16:21:02,015 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:21:02,026 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:21:02,035 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:21:02,049 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:21:02,056 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:21:02,101 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 16:21:02,179 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 16:21:02,235 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 16:21:27,760 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-27 16:21:28,166 - werkzeug - INFO -  * Restarting with stat
2025-05-27 16:21:29,098 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:21:29,100 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:21:29,101 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:21:29,102 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:21:29,103 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:21:29,569 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:21:30,134 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:21:30,138 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:21:30,159 - superspider - ERROR - 注册CSDN API路由失败: No module named 'bs4'
2025-05-27 16:21:30,166 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:21:30,173 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:21:30,181 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:21:30,185 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:21:30,193 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:21:30,215 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 16:21:30,254 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 16:21:30,274 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 16:45:58,587 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:45:58,590 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:45:58,590 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:45:58,590 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:45:58,590 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:45:58,639 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:45:58,640 - backend.utils.scheduler - WARNING - 调度器已在运行
2025-05-27 16:45:58,640 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:45:58,640 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:45:59,130 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:45:59,131 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:46:00,010 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:46:00,013 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:46:00,023 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:46:00,025 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:46:00,203 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:46:00,204 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:46:00,210 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:46:00,213 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:46:00,221 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:46:00,225 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:46:00,229 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:46:00,231 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:46:00,237 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:46:00,239 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:46:00,244 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:48:40,862 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:48:40,863 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:48:40,863 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:48:40,863 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:48:40,863 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:48:40,873 - backend.utils.scheduler - ERROR - 清理过期用户任务失败: 'NoneType' object has no attribute 'app_context'
2025-05-27 16:48:40,873 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 任务失败: 'NoneType' object has no attribute 'app_context'
2025-05-27 16:48:41,382 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:48:42,178 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:48:42,182 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:48:42,366 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:48:42,375 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:48:42,379 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:48:42,382 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:48:42,385 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:48:42,389 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:48:42,441 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 16:48:42,442 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 16:48:42,443 - werkzeug - INFO -  * Restarting with stat
2025-05-27 16:48:43,161 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:48:43,162 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:48:43,162 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:48:43,162 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:48:43,162 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:48:43,164 - backend.utils.scheduler - ERROR - 清理过期用户任务失败: 'NoneType' object has no attribute 'app_context'
2025-05-27 16:48:43,164 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 任务失败: 'NoneType' object has no attribute 'app_context'
2025-05-27 16:48:43,565 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:48:43,993 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:48:43,996 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:48:44,134 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:48:44,143 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:48:44,147 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:48:44,150 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:48:44,156 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:48:44,158 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:48:44,183 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 16:48:44,201 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 16:51:40,895 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:51:40,900 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:51:40,900 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:51:40,900 - backend.utils.scheduler - ERROR - 清理过期用户任务失败: 'NoneType' object has no attribute 'app_context'
2025-05-27 16:51:40,900 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:51:40,900 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 任务失败: 'NoneType' object has no attribute 'app_context'
2025-05-27 16:51:40,900 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:51:41,466 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:51:42,168 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:51:42,172 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:51:42,345 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:51:42,350 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:51:42,355 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:51:42,357 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:51:42,360 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:51:42,365 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:51:42,423 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 16:51:42,424 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 16:51:42,426 - werkzeug - INFO -  * Restarting with stat
2025-05-27 16:51:43,110 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:51:43,113 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:51:43,113 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:51:43,113 - backend.utils.scheduler - ERROR - 清理过期用户任务失败: 'NoneType' object has no attribute 'app_context'
2025-05-27 16:51:43,114 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:51:43,115 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 任务失败: 'NoneType' object has no attribute 'app_context'
2025-05-27 16:51:43,115 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:51:43,495 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:51:43,940 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:51:43,942 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:51:44,076 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:51:44,083 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:51:44,088 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:51:44,091 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:51:44,093 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:51:44,098 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:51:44,126 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 16:51:44,143 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 16:52:25,387 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:25] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 16:52:25,488 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:25] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 16:52:25,618 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:25] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 16:52:26,158 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "GET / HTTP/1.1" 200 -
2025-05-27 16:52:26,228 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 16:52:26,299 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 16:52:26,305 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 16:52:26,322 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 16:52:26,340 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 16:52:26,341 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 16:52:26,369 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 16:52:26,414 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 16:52:26,439 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 16:52:26,456 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 16:52:26,491 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 16:52:26,692 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "GET /static/js/csdn.js HTTP/1.1" 200 -
2025-05-27 16:52:26,699 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 16:52:26,740 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 16:52:26,784 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:26] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 16:52:27,229 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:27] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 16:52:27,241 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:27] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 16:52:27,294 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:52:27] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 16:52:53,815 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:52:53,816 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:52:53,817 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:52:53,817 - backend.utils.scheduler - ERROR - 清理过期用户任务失败: 'NoneType' object has no attribute 'app_context'
2025-05-27 16:52:53,817 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:52:53,817 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 任务失败: 'NoneType' object has no attribute 'app_context'
2025-05-27 16:52:53,817 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:52:54,563 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:52:55,149 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:52:55,152 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:52:55,305 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:52:55,315 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:52:55,323 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:52:55,330 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:52:55,334 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:52:55,337 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:52:55,415 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 16:52:55,416 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 16:52:55,418 - werkzeug - INFO -  * Restarting with stat
2025-05-27 16:52:56,250 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:52:56,251 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:52:56,251 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:52:56,252 - backend.utils.scheduler - ERROR - 清理过期用户任务失败: 'NoneType' object has no attribute 'app_context'
2025-05-27 16:52:56,252 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:52:56,252 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 任务失败: 'NoneType' object has no attribute 'app_context'
2025-05-27 16:52:56,252 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:52:56,661 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:52:57,121 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:52:57,125 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:52:57,302 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:52:57,307 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:52:57,317 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:52:57,322 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:52:57,330 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:52:57,334 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:52:57,375 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 16:52:57,398 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 16:53:36,535 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:53:36] "GET /api/status HTTP/1.1" 200 -
2025-05-27 16:57:27,539 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:57:27] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 17:01:05,852 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\run.py', reloading
2025-05-27 17:01:07,968 - werkzeug - INFO -  * Restarting with stat
2025-05-27 17:01:09,557 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 17:01:09,559 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 17:01:09,559 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 17:01:09,559 - backend.utils.scheduler - ERROR - 清理过期用户任务失败: 'NoneType' object has no attribute 'app_context'
2025-05-27 17:01:09,559 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 17:01:09,560 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 任务失败: 'NoneType' object has no attribute 'app_context'
2025-05-27 17:01:09,560 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 17:01:10,101 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 17:01:11,174 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 17:01:11,180 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 17:01:11,499 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 17:01:11,506 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 17:01:11,513 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 17:01:11,520 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 17:01:11,527 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 17:01:11,530 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 17:01:11,609 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 17:01:11,661 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 17:02:27,665 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:02:27] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 17:02:46,621 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 17:02:46,623 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 17:02:46,623 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 17:02:46,636 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 17:02:46,637 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 17:02:46,643 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 17:02:47,213 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 17:02:48,655 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 17:02:48,660 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 17:02:48,841 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 17:02:48,846 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 17:02:48,857 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 17:02:48,860 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 17:02:48,864 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 17:02:48,871 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 17:02:48,961 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 17:02:48,962 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 17:04:41,348 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:04:41] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 17:04:41,384 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:04:41] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 17:04:41,565 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:04:41] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 17:04:47,477 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/145213069
2025-05-27 17:04:47,484 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-27 17:04:47,497 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/145213069
2025-05-27 17:04:57,358 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:04:57] "GET /api/status HTTP/1.1" 200 -
2025-05-27 17:05:06,778 - backend.spiders.csdn_spider - ERROR - 请求CSDN文章失败: HTTPSConnectionPool(host='blog.csdn.net', port=443): Max retries exceeded with url: /fengbin2005/article/details/145213069 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-27 17:05:06,804 - backend.api.csdn_api - ERROR - 解析文章失败: 无法访问文章页面: HTTPSConnectionPool(host='blog.csdn.net', port=443): Max retries exceeded with url: /fengbin2005/article/details/145213069 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-27 17:05:06,951 - backend.api.csdn_api - ERROR - urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='blog.csdn.net', port=443): Max retries exceeded with url: /fengbin2005/article/details/145213069 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\csdn_spider.py", line 107, in _parse_article
    response = self.session.get(url, timeout=30)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python3.11.9\Lib\site-packages\requests\adapters.py", line 698, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='blog.csdn.net', port=443): Max retries exceeded with url: /fengbin2005/article/details/145213069 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\api\csdn_api.py", line 88, in parse_article
    result = spider.execute({"article_url": article_url})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\csdn_spider.py", line 62, in execute
    article_data = self._parse_article(article_url)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\spiders\csdn_spider.py", line 123, in _parse_article
    raise Exception(f"无法访问文章页面: {e}")
Exception: 无法访问文章页面: HTTPSConnectionPool(host='blog.csdn.net', port=443): Max retries exceeded with url: /fengbin2005/article/details/145213069 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

2025-05-27 17:05:07,004 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:05:07] "[35m[1mPOST /api/csdn/parse HTTP/1.1[0m" 500 -
2025-05-27 17:05:28,798 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:05:28] "GET /api/csdn/status HTTP/1.1" 200 -
2025-05-27 17:06:46,251 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/csdngeeknews/article/details/148220569?spm=1000.2115.3001.5927
2025-05-27 17:06:46,252 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-27 17:06:46,254 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/csdngeeknews/article/details/148220569?spm=1000.2115.3001.5927
2025-05-27 17:06:46,898 - backend.spiders.csdn_spider - INFO - 成功解析CSDN文章: 马斯克：已重新回到7×24小时工作状态；OpenHarmony开源代码规模超1.3亿行；传微软将砍掉Xbox游戏机 | 极客头条
2025-05-27 17:06:46,902 - backend.api.csdn_api - INFO - 成功解析CSDN文章: 马斯克：已重新回到7×24小时工作状态；OpenHarmony开源代码规模超1.3亿行；传微软将砍掉Xbox游戏机 | 极客头条
2025-05-27 17:06:46,905 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:06:46] "POST /api/csdn/parse HTTP/1.1" 200 -
2025-05-27 17:06:53,803 - backend.api.csdn_api - INFO - 开始下载CSDN文章: https://blog.csdn.net/csdngeeknews/article/details/148220569?spm=1000.2115.3001.5927
2025-05-27 17:06:53,803 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-27 17:06:53,804 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/csdngeeknews/article/details/148220569?spm=1000.2115.3001.5927
2025-05-27 17:06:54,384 - backend.spiders.csdn_spider - INFO - 成功解析CSDN文章: 马斯克：已重新回到7×24小时工作状态；OpenHarmony开源代码规模超1.3亿行；传微软将砍掉Xbox游戏机 | 极客头条
2025-05-27 17:06:54,693 - backend.utils.pdf_generator - INFO - PDF生成成功: D:\Program Files\VsCodeProject\SuperSpider\backend\api\..\..\downloads\马斯克：已重新回到7×24小时工作状态；OpenHarmony开源代码规模超1.3亿行；传微软将砍掉Xbox游戏机 _ 极客头条_20250527_170654.pdf
2025-05-27 17:06:54,697 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:06:54] "POST /api/csdn/download HTTP/1.1" 200 -
2025-05-27 17:09:42,316 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:09:42] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 17:14:42,317 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:14:42] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 17:19:42,580 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:19:42] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 17:24:42,485 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:24:42] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 17:29:42,351 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:29:42] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 17:34:42,648 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:34:42] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 17:36:16,169 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 17:36:16,170 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 17:36:16,170 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 17:36:16,182 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 17:36:16,183 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 17:36:16,186 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 17:36:16,730 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 17:36:17,817 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 17:36:17,822 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 17:36:17,833 - superspider - ERROR - 注册CSDN API路由失败: expected 'except' or 'finally' block (csdn_api.py, line 253)
2025-05-27 17:36:17,842 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 17:36:17,850 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 17:36:17,853 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 17:36:17,855 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 17:36:17,863 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 17:36:18,118 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 17:36:18,118 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 17:37:06,517 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:37:06] "GET / HTTP/1.1" 200 -
2025-05-27 17:37:06,610 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:37:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 17:37:06,614 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:37:06] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 17:37:06,622 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:37:06] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 17:37:06,630 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:37:06] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 17:37:06,632 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:37:06] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 17:37:06,683 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:37:06] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 17:37:06,691 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:37:06] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 17:37:06,736 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:37:06] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 17:37:06,739 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:37:06] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 17:37:06,740 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:37:06] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 17:37:06,752 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:37:06] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 17:37:06,757 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:37:06] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 17:37:06,758 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:37:06] "GET /static/js/csdn.js HTTP/1.1" 200 -
2025-05-27 17:37:06,997 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:37:06] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 17:37:07,025 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:37:07] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 17:37:07,437 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:37:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 17:37:07,443 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:37:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 17:37:07,505 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:37:07] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 17:37:13,833 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:37:13] "[33mPOST /api/csdn/parse HTTP/1.1[0m" 404 -
2025-05-27 17:37:49,705 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:37:49] "GET /api/status HTTP/1.1" 200 -
2025-05-27 17:40:05,771 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:40:05] "[33mPOST /api/csdn/parse HTTP/1.1[0m" 404 -
2025-05-27 17:40:12,155 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-27 17:40:12,729 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7486704905982364962
2025-05-27 17:40:19,009 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 鲁大魔对谈珍妮教授，干货满满 #鲁大魔 #创业者 #博弈 #海鸥联盟
2025-05-27 17:40:19,013 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:40:19] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-05-27 17:41:53,116 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 17:41:53,117 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 17:41:53,118 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 17:41:53,131 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 17:41:53,131 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 17:41:53,140 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 17:41:53,799 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 17:41:54,556 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 17:41:54,561 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 17:41:54,565 - superspider - ERROR - 注册CSDN API路由失败: expected 'except' or 'finally' block (csdn_api.py, line 253)
2025-05-27 17:41:54,570 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 17:41:54,576 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 17:41:54,580 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 17:41:54,582 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 17:41:54,585 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 17:41:54,686 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 17:41:54,686 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 17:42:09,119 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:42:09] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 17:44:40,799 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 17:44:40,800 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 17:44:40,800 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 17:44:40,818 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 17:44:40,830 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 17:44:40,837 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 17:44:41,601 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 17:44:42,462 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 17:44:42,467 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 17:44:42,676 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 17:44:42,680 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 17:44:42,684 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 17:44:42,690 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 17:44:42,697 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 17:44:42,699 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 17:44:42,865 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 17:44:42,866 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 17:45:01,390 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:01] "GET / HTTP/1.1" 200 -
2025-05-27 17:45:01,642 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:01] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 17:45:01,648 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 17:45:01,653 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:01] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 17:45:01,676 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:01] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 17:45:01,700 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:01] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 17:45:01,744 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:01] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 17:45:01,748 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:01] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 17:45:01,751 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:01] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 17:45:01,771 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:01] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 17:45:01,776 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:01] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 17:45:01,785 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:01] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 17:45:01,791 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:01] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 17:45:01,791 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:01] "[36mGET /static/js/csdn.js HTTP/1.1[0m" 304 -
2025-05-27 17:45:01,924 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:01] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 17:45:01,955 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:01] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 17:45:02,422 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:02] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 17:45:02,458 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:02] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 17:45:02,726 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:02] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 17:45:05,577 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:05] "[33mPOST /api/csdn/parse HTTP/1.1[0m" 404 -
2025-05-27 17:45:18,360 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:18] "GET / HTTP/1.1" 200 -
2025-05-27 17:45:18,386 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 17:45:18,414 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:18] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 17:45:18,424 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:18] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 17:45:18,426 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:18] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 17:45:18,429 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:18] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 17:45:18,433 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:18] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 17:45:18,485 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:18] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 17:45:18,488 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:18] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 17:45:18,498 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:18] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 17:45:18,499 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:18] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 17:45:18,519 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:18] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 17:45:18,533 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:18] "[36mGET /static/js/csdn.js HTTP/1.1[0m" 304 -
2025-05-27 17:45:18,547 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:18] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 17:45:18,570 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:18] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 17:45:18,592 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:18] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 17:45:19,066 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 17:45:19,080 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 17:45:19,103 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:19] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 17:45:24,609 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:24] "[33mPOST /api/csdn/parse HTTP/1.1[0m" 404 -
2025-05-27 17:45:44,954 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:45:44] "GET /api/status HTTP/1.1" 200 -
2025-05-27 17:46:37,151 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:46:37] "[33mGET /api/csdn/status HTTP/1.1[0m" 404 -
2025-05-27 17:47:43,544 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:47:43] "[33mGET /api/csdn/status HTTP/1.1[0m" 404 -
2025-05-27 17:48:33,990 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:48:33] "[33mGET /api/csdn/status HTTP/1.1[0m" 404 -
2025-05-27 17:49:07,712 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:49:07] "[33mPOST /api/csdn/parse HTTP/1.1[0m" 404 -
2025-05-27 17:49:08,963 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:49:08] "GET / HTTP/1.1" 200 -
2025-05-27 17:49:09,012 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:49:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 17:49:09,017 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:49:09] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 17:49:09,026 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:49:09] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 17:49:09,031 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:49:09] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 17:49:09,035 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:49:09] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 17:49:09,063 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:49:09] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 17:49:09,066 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:49:09] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 17:49:09,082 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:49:09] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 17:49:09,089 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:49:09] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 17:49:09,094 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:49:09] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 17:49:09,108 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:49:09] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 17:49:09,110 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:49:09] "[36mGET /static/js/csdn.js HTTP/1.1[0m" 304 -
2025-05-27 17:49:09,111 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:49:09] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 17:49:09,195 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:49:09] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 17:49:09,215 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:49:09] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 17:49:09,686 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:49:09] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 17:49:09,700 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:49:09] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 17:49:09,735 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:49:09] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 17:49:14,649 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:49:14] "[33mPOST /api/csdn/parse HTTP/1.1[0m" 404 -
2025-05-27 17:49:53,527 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 17:49:53,529 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 17:49:53,529 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 17:49:53,540 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 17:49:53,540 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 17:49:53,544 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 17:49:54,044 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 17:49:54,888 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 17:49:54,892 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 17:49:55,115 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 17:49:55,120 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 17:49:55,125 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 17:49:55,129 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 17:49:55,133 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 17:49:55,137 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 17:49:55,211 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 17:49:55,211 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 17:50:36,425 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:50:36] "[33mGET /api/csdn/status HTTP/1.1[0m" 404 -
2025-05-27 17:50:44,064 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:50:44] "GET / HTTP/1.1" 200 -
2025-05-27 17:50:44,175 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:50:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 17:50:44,175 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:50:44] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 17:50:44,179 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:50:44] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 17:50:44,191 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:50:44] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 17:50:44,194 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:50:44] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 17:50:44,220 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:50:44] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 17:50:44,221 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:50:44] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 17:50:44,284 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:50:44] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 17:50:44,300 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:50:44] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 17:50:44,316 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:50:44] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 17:50:44,337 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:50:44] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 17:50:44,354 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:50:44] "[36mGET /static/js/csdn.js HTTP/1.1[0m" 304 -
2025-05-27 17:50:44,371 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:50:44] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 17:50:44,579 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:50:44] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 17:50:44,617 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:50:44] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 17:50:45,040 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:50:45] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 17:50:45,085 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:50:45] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 17:50:45,133 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:50:45] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 17:50:51,278 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:50:51] "[33mPOST /api/csdn/parse HTTP/1.1[0m" 404 -
2025-05-27 17:51:04,511 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:51:04] "GET /api/status HTTP/1.1" 200 -
2025-05-27 17:51:53,325 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:51:53] "[33mGET /api/csdn/status HTTP/1.1[0m" 404 -
2025-05-27 17:52:29,045 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:52:29] "[33mGET /api/csdn/status HTTP/1.1[0m" 404 -
2025-05-27 17:52:44,245 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:52:44] "GET / HTTP/1.1" 200 -
2025-05-27 17:52:44,311 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:52:44] "GET /static/css/style.css HTTP/1.1" 200 -
2025-05-27 17:52:46,055 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:52:46] "GET /static/css/user.css HTTP/1.1" 200 -
2025-05-27 17:52:46,084 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:52:46] "GET /static/css/downloads.css HTTP/1.1" 200 -
2025-05-27 17:52:46,099 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:52:46] "GET /static/images/wechat-qrcode.jpg HTTP/1.1" 200 -
2025-05-27 17:52:46,107 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:52:46] "GET /static/js/permissions.js HTTP/1.1" 200 -
2025-05-27 17:52:46,137 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:52:46] "GET /static/js/validation.js HTTP/1.1" 200 -
2025-05-27 17:52:46,175 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:52:46] "GET /static/js/auth.js HTTP/1.1" 200 -
2025-05-27 17:52:46,424 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:52:46] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-27 17:52:46,430 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:52:46] "GET /static/js/downloads.js HTTP/1.1" 200 -
2025-05-27 17:52:46,485 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:52:46] "GET /static/js/permission-management.js HTTP/1.1" 200 -
2025-05-27 17:52:46,485 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:52:46] "GET /static/js/video-controls.js HTTP/1.1" 200 -
2025-05-27 17:52:46,497 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:52:46] "GET /static/js/csdn.js HTTP/1.1" 200 -
2025-05-27 17:52:46,546 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:52:46] "GET /static/js/direct-auth.js HTTP/1.1" 200 -
2025-05-27 17:52:46,975 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:52:46] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-05-27 17:52:47,243 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:52:47] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-05-27 17:52:47,290 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:52:47] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-05-27 17:52:47,457 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:52:47] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-05-27 17:52:47,611 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:52:47] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-05-27 17:52:47,619 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:52:47] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-05-27 17:53:07,649 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:53:07] "GET / HTTP/1.1" 200 -
2025-05-27 17:53:07,685 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:53:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 17:53:07,882 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:53:07] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 17:53:07,993 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:53:07] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 17:53:07,997 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:53:07] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 17:53:08,003 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:53:08] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 17:53:08,013 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:53:08] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 17:53:08,117 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:53:08] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 17:53:08,220 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:53:08] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 17:53:08,332 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:53:08] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 17:53:08,432 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:53:08] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 17:53:08,450 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:53:08] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 17:53:08,454 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:53:08] "[36mGET /static/js/csdn.js HTTP/1.1[0m" 304 -
2025-05-27 17:53:08,481 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:53:08] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 17:53:08,898 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:53:08] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-05-27 17:53:09,105 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:53:09] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-05-27 17:53:09,332 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:53:09] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-05-27 17:53:09,350 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:53:09] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-05-27 17:53:09,644 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:53:09] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-05-27 17:53:09,653 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:53:09] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-05-27 17:55:45,543 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:55:45] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 17:57:38,800 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:57:38] "GET / HTTP/1.1" 200 -
2025-05-27 17:57:38,899 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:57:38] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 17:57:39,203 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:57:39] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 17:57:39,203 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:57:39] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 17:57:39,237 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:57:39] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 17:57:39,241 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:57:39] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 17:57:39,953 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:57:39] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 17:57:40,086 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:57:40] "GET /static/js/auth.js HTTP/1.1" 200 -
2025-05-27 17:57:40,098 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:57:40] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 17:57:40,168 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:57:40] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 17:57:40,198 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:57:40] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 17:57:40,203 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:57:40] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 17:57:40,384 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:57:40] "[36mGET /static/js/csdn.js HTTP/1.1[0m" 304 -
2025-05-27 17:57:40,511 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:57:40] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 17:57:40,842 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:57:40] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-05-27 17:57:41,101 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:57:41] "[31m[1mGET /api/auth/check-auth HTTP/1.1[0m" 401 -
2025-05-27 17:57:41,165 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:57:41] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-05-27 17:57:41,333 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:57:41] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-05-27 17:57:41,476 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:57:41] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-05-27 17:57:41,481 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:57:41] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-05-27 17:58:10,603 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:58:10] "[32mGET /api/permission/check HTTP/1.1[0m" 302 -
2025-05-27 17:58:10,868 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:58:10] "[31m[1mGET /api/auth/login?next=/api/permission/check HTTP/1.1[0m" 405 -
2025-05-27 17:59:39,184 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:59:39] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-27 17:59:49,770 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:59:49] "GET / HTTP/1.1" 200 -
2025-05-27 17:59:49,801 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:59:49] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 17:59:50,000 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:59:50] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 17:59:50,105 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:59:50] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 17:59:50,106 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:59:50] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 17:59:50,109 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:59:50] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 17:59:50,131 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:59:50] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 17:59:50,131 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:59:50] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 17:59:50,313 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:59:50] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 17:59:50,443 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:59:50] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 17:59:50,444 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:59:50] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 17:59:50,444 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:59:50] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 17:59:50,460 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:59:50] "[36mGET /static/js/csdn.js HTTP/1.1[0m" 304 -
2025-05-27 17:59:50,460 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:59:50] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 17:59:50,784 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:59:50] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 17:59:51,048 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:59:51] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 17:59:51,095 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:59:51] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 17:59:51,292 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:59:51] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 17:59:51,437 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:59:51] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 17:59:58,157 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:59:58] "[33mPOST /api/csdn/parse HTTP/1.1[0m" 404 -
2025-05-27 18:00:29,889 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:00:29] "[33mPOST /api/csdn/parse HTTP/1.1[0m" 404 -
2025-05-27 18:00:45,461 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:00:45] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:03:10,671 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:03:10] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:03:19,484 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:03:19] "GET /api/status HTTP/1.1" 200 -
2025-05-27 18:04:50,716 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:04:50] "[33mGET /api/csdn/status HTTP/1.1[0m" 404 -
2025-05-27 18:04:51,867 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:04:51] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:05:45,311 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:05:45] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:07:11,392 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:07:11] "[33mGET /api/csdn/status HTTP/1.1[0m" 404 -
2025-05-27 18:08:10,732 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:08:10] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:09:30,341 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 18:09:30,342 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 18:09:30,342 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 18:09:30,353 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 18:09:30,358 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 18:09:30,366 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 18:09:30,973 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 18:09:32,172 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 18:09:32,177 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 18:09:32,459 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 18:09:32,465 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 18:09:32,474 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 18:09:32,481 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 18:09:32,488 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 18:09:32,494 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 18:09:32,692 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 18:09:32,709 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:09:37,239 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:09:37] "GET / HTTP/1.1" 200 -
2025-05-27 18:09:37,443 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:09:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 18:09:37,444 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:09:37] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 18:09:37,473 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:09:37] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 18:09:37,476 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:09:37] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 18:09:37,478 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:09:37] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 18:09:37,545 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:09:37] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 18:09:37,558 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:09:37] "GET /static/js/auth.js HTTP/1.1" 200 -
2025-05-27 18:09:37,558 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:09:37] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 18:09:37,574 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:09:37] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 18:09:37,577 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:09:37] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 18:09:37,606 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:09:37] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 18:09:37,626 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:09:37] "[36mGET /static/js/csdn.js HTTP/1.1[0m" 304 -
2025-05-27 18:09:37,663 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:09:37] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 18:09:38,495 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:09:38] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 18:09:38,607 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:09:38] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 18:09:38,670 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:09:38] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:09:38,707 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:09:38] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:09:38,841 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:09:38] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 18:09:45,602 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:09:45] "[33mPOST /api/csdn/parse HTTP/1.1[0m" 404 -
2025-05-27 18:14:39,486 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:14:39] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:16:02,414 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 18:16:02,416 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 18:16:02,416 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 18:16:02,439 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 18:16:02,445 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 18:16:02,449 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 18:16:03,031 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 18:16:03,804 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 18:16:03,807 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 18:16:04,015 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 18:16:04,021 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 18:16:04,032 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 18:16:04,039 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 18:16:04,045 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 18:16:04,049 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 18:16:04,134 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 18:16:04,136 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:16:09,244 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:09] "GET / HTTP/1.1" 200 -
2025-05-27 18:16:10,232 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:10] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 18:16:10,269 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:10] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 18:16:10,692 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:10] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 18:16:10,718 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:10] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 18:16:10,761 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:10] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 18:16:10,799 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:10] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 18:16:11,066 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:11] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 18:16:11,106 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:11] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 18:16:11,129 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:11] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 18:16:11,172 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:11] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 18:16:11,318 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:11] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 18:16:11,366 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:11] "[36mGET /static/js/csdn.js HTTP/1.1[0m" 304 -
2025-05-27 18:16:11,494 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:11] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 18:16:11,626 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:11] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 18:16:11,640 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:11] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 18:16:12,102 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:12] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:16:12,116 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:12] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:16:12,172 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:12] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 18:16:17,027 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:17] "[33mPOST /api/csdn/parse HTTP/1.1[0m" 404 -
2025-05-27 18:16:21,037 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 18:16:21,040 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:21] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 18:16:21,051 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:21] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 18:16:21,150 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:21] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-05-27 18:16:24,368 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:24] "[33mPOST /api/csdn/parse HTTP/1.1[0m" 404 -
2025-05-27 18:17:44,576 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:17:44] "[33mGET /api/csdn/status HTTP/1.1[0m" 404 -
2025-05-27 18:18:08,909 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:18:08] "[33mGET /api/csdn/status HTTP/1.1[0m" 404 -
2025-05-27 18:20:22,222 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:20:22] "GET / HTTP/1.1" 200 -
2025-05-27 18:20:22,407 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:20:22] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 18:20:22,850 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:20:22] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 18:20:22,858 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:20:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 18:20:22,878 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:20:22] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 18:20:22,906 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:20:22] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 18:20:22,912 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:20:22] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 18:20:22,957 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:20:22] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 18:20:23,223 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:20:23] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 18:20:23,238 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:20:23] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 18:20:23,389 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:20:23] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 18:20:23,470 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:20:23] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 18:20:23,582 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:20:23] "GET /static/js/csdn.js HTTP/1.1" 200 -
2025-05-27 18:20:23,584 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:20:23] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 18:20:23,926 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:20:23] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 18:20:24,189 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:20:24] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 18:20:24,278 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:20:24] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:20:24,431 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:20:24] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:20:24,671 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:20:24] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 18:20:29,860 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:20:29] "[33mPOST /api/csdn/parse HTTP/1.1[0m" 404 -
2025-05-27 18:21:12,340 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:21:12] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:21:31,323 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 18:21:31,324 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 18:21:31,324 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 18:21:31,335 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 18:21:31,335 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 18:21:31,352 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 18:21:31,848 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 18:21:32,603 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 18:21:32,607 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 18:21:32,768 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 18:21:32,773 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 18:21:32,777 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 18:21:32,780 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 18:21:32,783 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 18:21:32,785 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 18:21:32,850 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 18:21:32,850 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:21:44,684 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:21:44] "GET / HTTP/1.1" 200 -
2025-05-27 18:21:45,019 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:21:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 18:21:45,019 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:21:45] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 18:21:45,037 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:21:45] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 18:21:45,108 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:21:45] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 18:21:45,180 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:21:45] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 18:21:45,187 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:21:45] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 18:21:45,197 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:21:45] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 18:21:45,250 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:21:45] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 18:21:45,253 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:21:45] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 18:21:45,270 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:21:45] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 18:21:45,279 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:21:45] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 18:21:45,300 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:21:45] "GET /static/js/csdn.js HTTP/1.1" 200 -
2025-05-27 18:21:45,316 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:21:45] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 18:21:45,568 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:21:45] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 18:21:45,578 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:21:45] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 18:21:46,068 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:21:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:21:46,078 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:21:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:21:46,117 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:21:46] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 18:21:50,836 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:21:50] "[33mPOST /api/csdn/parse HTTP/1.1[0m" 404 -
2025-05-27 18:26:12,516 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:12] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:26:12,520 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 18:26:12,522 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 18:26:12,522 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 18:26:12,539 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 18:26:12,540 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 18:26:12,546 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 18:26:13,120 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 18:26:14,354 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 18:26:14,358 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 18:26:14,498 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 18:26:14,503 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 18:26:14,507 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 18:26:14,510 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 18:26:14,514 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 18:26:14,519 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 18:26:14,574 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 18:26:14,574 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:26:16,590 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:16] "GET / HTTP/1.1" 200 -
2025-05-27 18:26:16,872 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 18:26:16,872 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:16] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 18:26:16,883 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:16] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 18:26:16,893 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:16] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 18:26:16,894 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:16] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 18:26:16,931 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:16] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 18:26:16,932 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:16] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 18:26:16,933 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:16] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 18:26:16,974 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:16] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 18:26:16,978 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:16] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 18:26:16,991 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:16] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 18:26:16,995 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:16] "[36mGET /static/js/csdn.js HTTP/1.1[0m" 304 -
2025-05-27 18:26:17,003 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:17] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 18:26:17,366 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:17] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 18:26:17,384 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:17] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 18:26:17,867 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:17] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:26:17,877 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:17] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:26:17,953 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:17] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 18:26:22,726 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:22] "GET / HTTP/1.1" 200 -
2025-05-27 18:26:22,744 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 18:26:22,746 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:22] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 18:26:22,749 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:22] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 18:26:22,758 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:22] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 18:26:22,762 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:22] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 18:26:22,765 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:22] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 18:26:22,776 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:22] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 18:26:22,777 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:22] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 18:26:22,779 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:22] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 18:26:22,806 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:22] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 18:26:22,815 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:22] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 18:26:22,816 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:22] "[36mGET /static/js/csdn.js HTTP/1.1[0m" 304 -
2025-05-27 18:26:22,823 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:22] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 18:26:22,863 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:22] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 18:26:22,879 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:22] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 18:26:23,364 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:23] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:26:23,374 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:23] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:26:23,411 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:23] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 18:26:26,796 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:26] "[33mPOST /api/csdn/parse HTTP/1.1[0m" 404 -
2025-05-27 18:26:34,544 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:34] "GET / HTTP/1.1" 200 -
2025-05-27 18:26:34,572 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:34] "GET /static/css/style.css HTTP/1.1" 200 -
2025-05-27 18:26:34,576 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:34] "GET /static/css/user.css HTTP/1.1" 200 -
2025-05-27 18:26:34,577 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:34] "GET /static/css/downloads.css HTTP/1.1" 200 -
2025-05-27 18:26:34,578 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:34] "GET /static/images/wechat-qrcode.jpg HTTP/1.1" 200 -
2025-05-27 18:26:34,582 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:34] "GET /static/js/permissions.js HTTP/1.1" 200 -
2025-05-27 18:26:34,603 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:34] "GET /static/js/validation.js HTTP/1.1" 200 -
2025-05-27 18:26:34,616 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:34] "GET /static/js/downloads.js HTTP/1.1" 200 -
2025-05-27 18:26:34,617 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:34] "GET /static/js/auth.js HTTP/1.1" 200 -
2025-05-27 18:26:34,627 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:34] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-27 18:26:34,630 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:34] "GET /static/js/video-controls.js HTTP/1.1" 200 -
2025-05-27 18:26:34,633 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:34] "GET /static/js/permission-management.js HTTP/1.1" 200 -
2025-05-27 18:26:34,634 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:34] "GET /static/js/csdn.js HTTP/1.1" 200 -
2025-05-27 18:26:34,649 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:34] "GET /static/js/direct-auth.js HTTP/1.1" 200 -
2025-05-27 18:26:35,906 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:35] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 18:26:35,937 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:35] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 18:26:36,392 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:36] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:26:36,393 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:36] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:26:36,417 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:36] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 18:26:38,423 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:38] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-27 18:26:39,342 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:39] "GET / HTTP/1.1" 200 -
2025-05-27 18:26:39,364 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:39] "GET /static/css/style.css HTTP/1.1" 200 -
2025-05-27 18:26:39,373 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:39] "GET /static/css/downloads.css HTTP/1.1" 200 -
2025-05-27 18:26:39,374 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:39] "GET /static/js/permissions.js HTTP/1.1" 200 -
2025-05-27 18:26:39,375 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:39] "GET /static/images/wechat-qrcode.jpg HTTP/1.1" 200 -
2025-05-27 18:26:39,382 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:39] "GET /static/css/user.css HTTP/1.1" 200 -
2025-05-27 18:26:39,383 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:39] "GET /static/js/validation.js HTTP/1.1" 200 -
2025-05-27 18:26:39,388 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:39] "GET /static/js/auth.js HTTP/1.1" 200 -
2025-05-27 18:26:39,395 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:39] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-27 18:26:39,400 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:39] "GET /static/js/downloads.js HTTP/1.1" 200 -
2025-05-27 18:26:39,410 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:39] "GET /static/js/video-controls.js HTTP/1.1" 200 -
2025-05-27 18:26:39,412 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:39] "GET /static/js/permission-management.js HTTP/1.1" 200 -
2025-05-27 18:26:39,422 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:39] "GET /static/js/csdn.js HTTP/1.1" 200 -
2025-05-27 18:26:39,423 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:39] "GET /static/js/direct-auth.js HTTP/1.1" 200 -
2025-05-27 18:26:39,567 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:39] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 18:26:39,568 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:39] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 18:26:40,059 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:40] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:26:40,068 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:40] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:26:40,085 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:40] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 18:26:40,756 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:40] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-27 18:26:40,944 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:40] "GET / HTTP/1.1" 200 -
2025-05-27 18:26:40,967 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:40] "GET /static/css/style.css HTTP/1.1" 200 -
2025-05-27 18:26:40,975 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:40] "GET /static/css/downloads.css HTTP/1.1" 200 -
2025-05-27 18:26:40,975 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:40] "GET /static/images/wechat-qrcode.jpg HTTP/1.1" 200 -
2025-05-27 18:26:40,976 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:40] "GET /static/js/permissions.js HTTP/1.1" 200 -
2025-05-27 18:26:40,976 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:40] "GET /static/css/user.css HTTP/1.1" 200 -
2025-05-27 18:26:40,977 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:40] "GET /static/js/validation.js HTTP/1.1" 200 -
2025-05-27 18:26:40,984 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:40] "GET /static/js/auth.js HTTP/1.1" 200 -
2025-05-27 18:26:40,996 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:40] "GET /static/js/downloads.js HTTP/1.1" 200 -
2025-05-27 18:26:40,998 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:40] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-27 18:26:41,003 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:41] "GET /static/js/video-controls.js HTTP/1.1" 200 -
2025-05-27 18:26:41,004 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:41] "GET /static/js/permission-management.js HTTP/1.1" 200 -
2025-05-27 18:26:41,008 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:41] "GET /static/js/direct-auth.js HTTP/1.1" 200 -
2025-05-27 18:26:41,008 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:41] "GET /static/js/csdn.js HTTP/1.1" 200 -
2025-05-27 18:26:41,521 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:41] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 18:26:41,521 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:41] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 18:26:42,014 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:42] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:26:42,021 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:42] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:26:42,037 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:42] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 18:26:44,171 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:44] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-27 18:27:12,534 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:27:12] "GET /api/auth/profile HTTP/1.1" 200 -
2025-05-27 18:27:13,685 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:27:13] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:27:13,706 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:27:13] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 18:27:21,951 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:27:21] "GET /api/activation/list?per_page=50 HTTP/1.1" 200 -
2025-05-27 18:27:30,517 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:27:30] "GET /api/auth/profile HTTP/1.1" 200 -
2025-05-27 18:27:33,820 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:27:33] "GET /api/download/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 18:27:33,835 - backend.api.download_api - ERROR - 获取下载统计信息失败: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?
2025-05-27 18:27:33,838 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:27:33] "[35m[1mGET /api/download/stats HTTP/1.1[0m" 500 -
2025-05-27 18:27:33,839 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:27:33] "GET /api/download/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 18:27:33,851 - backend.api.download_api - ERROR - 获取下载统计信息失败: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?
2025-05-27 18:27:33,854 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:27:33] "[35m[1mGET /api/download/stats HTTP/1.1[0m" 500 -
2025-05-27 18:27:50,032 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:27:50] "[33mPOST /api/csdn/parse HTTP/1.1[0m" 404 -
2025-05-27 18:31:43,074 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:31:43] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:31:43,408 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:31:43] "[33mGET /api/csdn/status HTTP/1.1[0m" 404 -
2025-05-27 18:33:18,271 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:33:18] "[33mGET /api/csdn/test HTTP/1.1[0m" 404 -
2025-05-27 18:36:18,648 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:36:18] "[33mGET /api/csdn/test HTTP/1.1[0m" 404 -
2025-05-27 18:36:42,570 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:36:42] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:37:16,265 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 18:37:16,267 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 18:37:16,268 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 18:37:16,290 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 18:37:16,291 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 18:37:16,299 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 18:37:16,952 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 18:37:18,040 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 18:37:18,048 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 18:37:18,301 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 18:37:18,306 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 18:37:18,312 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 18:37:18,317 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 18:37:18,320 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 18:37:18,323 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 18:37:18,489 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 18:37:18,491 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:38:29,757 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:38:29] "[33mGET /api/csdn/test HTTP/1.1[0m" 404 -
2025-05-27 18:38:44,998 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:38:44] "[33mGET /api/csdn/test HTTP/1.1[0m" 404 -
2025-05-27 18:38:45,222 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:38:45] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-27 18:39:33,387 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:39:33] "[33mGET /api/csdn/test HTTP/1.1[0m" 404 -
2025-05-27 18:39:49,071 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:39:49] "[33mGET /api/csdn/test HTTP/1.1[0m" 404 -
2025-05-27 18:41:42,585 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:41:42] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:46:42,737 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:46:42] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:51:42,589 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:51:42] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:56:42,709 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:56:42] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 18:57:25,440 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 18:57:25,441 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 18:57:25,441 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 18:57:25,456 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 18:57:25,456 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 18:57:25,470 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 18:57:26,145 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 18:57:26,995 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 18:57:27,001 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 18:57:27,167 - superspider - ERROR - 注册CSDN API路由失败: BaseSpider.__init__() missing 1 required positional argument: 'name'
2025-05-27 18:57:27,173 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 18:57:27,184 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 18:57:27,190 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 18:57:27,194 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 18:57:27,200 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 18:57:27,284 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 18:57:27,285 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 19:01:02,418 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 19:01:02,426 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 19:01:02,427 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 19:01:02,454 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 19:01:02,454 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 19:01:02,485 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 19:01:03,213 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 19:01:03,935 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 19:01:03,940 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 19:01:04,076 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-27 19:01:04,077 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 19:01:04,083 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 19:01:04,090 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 19:01:04,092 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 19:01:04,096 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 19:01:04,098 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 19:01:04,158 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 19:01:04,159 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 19:02:08,457 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 19:07:08,340 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:07:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 19:12:08,302 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:12:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 19:17:08,288 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:17:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 19:22:08,273 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:22:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 19:27:08,281 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:27:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 19:32:08,316 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:32:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 19:33:50,726 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:33:50] "[33mGET /api/csdn/status HTTP/1.1[0m" 404 -
2025-05-27 19:34:17,804 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 19:34:17,805 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 19:34:17,805 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 19:34:17,815 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 19:34:17,815 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 19:34:17,821 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 19:34:18,253 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 19:34:19,018 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 19:34:19,022 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 19:34:19,142 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-27 19:34:19,143 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 19:34:19,148 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 19:34:19,153 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 19:34:19,157 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 19:34:19,160 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 19:34:19,165 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 19:34:19,228 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 19:34:19,228 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 19:34:35,533 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:35] "GET / HTTP/1.1" 200 -
2025-05-27 19:34:35,658 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 19:34:35,662 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:35] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 19:34:35,666 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:35] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 19:34:35,673 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:35] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 19:34:35,673 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:35] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 19:34:35,698 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:35] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 19:34:35,704 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:35] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 19:34:35,718 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:35] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 19:34:35,728 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:35] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 19:34:35,733 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:35] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 19:34:35,734 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:35] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 19:34:35,747 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:35] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 19:34:35,748 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:35] "GET /static/js/csdn.js HTTP/1.1" 200 -
2025-05-27 19:34:35,918 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:35] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 19:34:35,941 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:35] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 19:34:36,399 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:36] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 19:34:36,414 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:36] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 19:34:36,474 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:36] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 19:34:42,391 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:42] "GET /?article_url=https://blog.csdn.net/fengbin2005/article/details/145213069&email=<EMAIL> HTTP/1.1" 200 -
2025-05-27 19:34:42,420 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 19:34:42,420 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:42] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 19:34:42,422 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:42] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 19:34:42,424 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:42] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 19:34:42,435 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:42] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 19:34:42,435 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:42] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 19:34:42,438 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:42] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 19:34:42,445 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:42] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 19:34:42,452 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:42] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 19:34:42,465 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:42] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 19:34:42,466 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:42] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 19:34:42,467 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:42] "[36mGET /static/js/csdn.js HTTP/1.1[0m" 304 -
2025-05-27 19:34:42,470 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:42] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 19:34:42,483 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:42] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 19:34:42,495 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:42] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 19:34:42,987 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:42] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 19:34:42,995 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:42] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 19:34:43,014 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:43] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 19:34:49,238 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:49] "GET /?article_url=https://blog.csdn.net/fengbin2005/article/details/145213069&email=<EMAIL> HTTP/1.1" 200 -
2025-05-27 19:34:49,261 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:49] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 19:34:49,279 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:49] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 19:34:49,281 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:49] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 19:34:49,283 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:49] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 19:34:49,294 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:49] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 19:34:49,296 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:49] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 19:34:49,300 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:49] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 19:34:49,305 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:49] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 19:34:49,317 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:49] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 19:34:49,323 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:49] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 19:34:49,331 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:49] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 19:34:49,333 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:49] "[36mGET /static/js/csdn.js HTTP/1.1[0m" 304 -
2025-05-27 19:34:49,338 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:49] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 19:34:49,359 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:49] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 19:34:49,366 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:49] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 19:34:49,857 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:49] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 19:34:49,862 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:49] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 19:34:49,880 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:34:49] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 19:39:03,182 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 19:39:03,184 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 19:39:03,184 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 19:39:03,194 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 19:39:03,196 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 19:39:03,199 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 19:39:03,696 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 19:39:04,509 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 19:39:04,515 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 19:39:04,521 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 19:39:04,529 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 19:39:04,534 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 19:39:04,538 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 19:39:04,541 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 19:39:04,616 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 19:39:04,617 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 19:39:18,505 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:18] "GET / HTTP/1.1" 200 -
2025-05-27 19:39:18,594 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:18] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 19:39:18,595 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 19:39:18,598 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:18] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 19:39:18,617 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:18] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 19:39:18,622 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:18] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 19:39:18,625 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:18] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 19:39:18,646 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:18] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 19:39:18,662 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:18] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 19:39:18,678 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:18] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 19:39:18,699 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:18] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 19:39:18,714 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:18] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 19:39:18,727 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:18] "[33mGET /static/js/csdn.js HTTP/1.1[0m" 404 -
2025-05-27 19:39:18,742 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:18] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 19:39:19,062 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:19] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 19:39:19,072 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:19] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 19:39:19,546 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 19:39:19,561 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:19] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 19:39:19,617 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:19] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 19:39:25,181 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:25] "GET /?article_url=https://blog.csdn.net/fengbin2005/article/details/145213069&email=<EMAIL> HTTP/1.1" 200 -
2025-05-27 19:39:25,203 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 19:39:25,206 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:25] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 19:39:25,208 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:25] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 19:39:25,219 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:25] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 19:39:25,220 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:25] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 19:39:25,228 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:25] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 19:39:25,231 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:25] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 19:39:25,246 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:25] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 19:39:25,252 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:25] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 19:39:25,255 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:25] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 19:39:25,256 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:25] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 19:39:25,270 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:25] "[33mGET /static/js/csdn.js HTTP/1.1[0m" 404 -
2025-05-27 19:39:25,275 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:25] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 19:39:25,306 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:25] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 19:39:25,323 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:25] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 19:39:25,803 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:25] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 19:39:25,809 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:25] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 19:39:25,825 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:25] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 19:39:29,654 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-27 19:39:29,658 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7486704905982364962
2025-05-27 19:39:34,076 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 鲁大魔对谈珍妮教授，干货满满 #鲁大魔 #创业者 #博弈 #海鸥联盟
2025-05-27 19:39:34,079 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:34] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-05-27 19:39:36,895 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:39:36] "POST /api/download/record HTTP/1.1" 200 -
2025-05-27 19:41:45,605 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 19:41:47,823 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 看一下#cos #鸣潮 #椿cos #漫展
2025-05-27 19:41:47,826 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:41:47] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 19:42:50,279 - backend.api.bilibili_api - INFO - 开始解析哔哩哔哩视频: https://www.bilibili.com/video/BV1uzAZeVEYA/?share_source=copy_web&vd_source=6b1a893bc1d403075a53930e9bd9ff7d
2025-05-27 19:42:50,280 - backend.spiders.base_spider - INFO - 初始化爬虫: 哔哩哔哩爬虫
2025-05-27 19:42:50,720 - backend.spiders.bilibili_spider - INFO - 获取到最终URL: https://www.bilibili.com/video/BV1uzAZeVEYA/
2025-05-27 19:42:51,176 - backend.spiders.bilibili_spider - INFO - 视频文件已存在: 小程序怎么上架？什么是备案？个人开发者怎么选择服务器？
2025-05-27 19:42:51,179 - backend.spiders.bilibili_spider - INFO - 成功解析视频信息: 小程序怎么上架？什么是备案？个人开发者怎么选择服务器？
2025-05-27 19:42:51,179 - backend.api.bilibili_api - INFO - 成功解析视频信息: 小程序怎么上架？什么是备案？个人开发者怎么选择服务器？
2025-05-27 19:42:51,180 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:42:51] "POST /api/bilibili/parse HTTP/1.1" 200 -
2025-05-27 19:42:51,190 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:42:51] "[33mGET /u002F/u002Fi2.hdslb.com/u002Fbfs/u002Farchive/u002Fc16e09fb1a1a2d86149343e9df435132701cf836.jpg HTTP/1.1[0m" 404 -
2025-05-27 19:42:51,212 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:42:51] "[35m[1mGET /media/videos/小程序怎么上架？什么是备案？个人开发者怎么选择服务器？.mp4 HTTP/1.1[0m" 206 -
2025-05-27 19:42:51,242 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:42:51] "[35m[1mGET /media/videos/小程序怎么上架？什么是备案？个人开发者怎么选择服务器？.mp4 HTTP/1.1[0m" 206 -
2025-05-27 19:43:05,764 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:43:05] "GET /api/download/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 19:43:05,786 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:43:05] "GET /api/download/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 19:43:05,839 - backend.api.download_api - ERROR - 获取下载统计信息失败: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?
2025-05-27 19:43:05,841 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:43:05] "[35m[1mGET /api/download/stats HTTP/1.1[0m" 500 -
2025-05-27 19:43:05,877 - backend.api.download_api - ERROR - 获取下载统计信息失败: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?
2025-05-27 19:43:05,882 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:43:05] "[35m[1mGET /api/download/stats HTTP/1.1[0m" 500 -
2025-05-27 19:43:10,933 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 19:43:12,068 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 19:43:12,069 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:43:12] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 19:43:15,321 - backend.api.download_api - ERROR - 创建下载记录失败: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?
2025-05-27 19:43:15,321 - backend.api.download_api - ERROR - 创建下载记录失败: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?
2025-05-27 19:43:15,322 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:43:15] "[35m[1mPOST /api/download/record HTTP/1.1[0m" 500 -
2025-05-27 19:43:15,323 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:43:15] "[35m[1mPOST /api/download/record HTTP/1.1[0m" 500 -
2025-05-27 19:43:23,805 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:43:23] "GET /api/download/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 19:43:23,827 - backend.api.download_api - ERROR - 获取下载统计信息失败: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?
2025-05-27 19:43:23,828 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:43:23] "[35m[1mGET /api/download/stats HTTP/1.1[0m" 500 -
2025-05-27 19:43:23,828 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:43:23] "GET /api/download/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 19:43:23,846 - backend.api.download_api - ERROR - 获取下载统计信息失败: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?
2025-05-27 19:43:23,851 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:43:23] "[35m[1mGET /api/download/stats HTTP/1.1[0m" 500 -
2025-05-27 19:44:25,824 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:44:25] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 19:49:25,812 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:49:25] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 19:51:21,677 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:51:21] "GET /api/auth/profile HTTP/1.1" 200 -
2025-05-27 19:51:22,470 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:51:22] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 19:51:22,501 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:51:22] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 19:52:47,599 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:52:47] "GET /api/download/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 19:52:47,624 - backend.api.download_api - ERROR - 获取下载统计信息失败: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?
2025-05-27 19:52:47,624 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:52:47] "GET /api/download/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 19:52:47,627 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:52:47] "[35m[1mGET /api/download/stats HTTP/1.1[0m" 500 -
2025-05-27 19:52:47,659 - backend.api.download_api - ERROR - 获取下载统计信息失败: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?
2025-05-27 19:52:47,661 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:52:47] "[35m[1mGET /api/download/stats HTTP/1.1[0m" 500 -
2025-05-27 19:52:51,934 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:52:51] "GET /api/download/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 19:52:51,945 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:52:51] "GET /api/download/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 19:52:51,962 - backend.api.download_api - ERROR - 获取下载统计信息失败: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?
2025-05-27 19:52:51,964 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:52:51] "[35m[1mGET /api/download/stats HTTP/1.1[0m" 500 -
2025-05-27 19:52:51,978 - backend.api.download_api - ERROR - 获取下载统计信息失败: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances?
2025-05-27 19:52:51,980 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:52:51] "[35m[1mGET /api/download/stats HTTP/1.1[0m" 500 -
2025-05-27 19:54:26,523 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:54:26] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 19:59:26,576 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:59:26] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 20:16:34,221 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 20:16:34,224 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 20:16:34,224 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 20:16:34,244 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 20:16:34,244 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 20:16:34,255 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 20:16:34,788 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 20:16:35,675 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 20:16:35,682 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 20:16:35,687 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 20:16:35,695 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 20:16:35,697 - superspider - ERROR - 注册管理员API路由失败: No module named 'backend.models.download'
2025-05-27 20:16:35,700 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 20:16:35,703 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 20:16:35,773 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 20:16:35,774 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
