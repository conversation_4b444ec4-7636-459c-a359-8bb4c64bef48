#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
网页截图生成工具
用于生成CSDN文章的长截图
"""

import os
import logging
from typing import Optional
from datetime import datetime
import tempfile

logger = logging.getLogger(__name__)

class ScreenshotGenerator:
    """网页截图生成器"""
    
    def __init__(self, output_dir: str = None):
        """
        初始化截图生成器
        
        Args:
            output_dir: 输出目录，如果为None则使用临时目录
        """
        self.output_dir = output_dir or tempfile.gettempdir()
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
    
    def generate_article_screenshot(self, article_url: str, article_title: str = None) -> str:
        """
        生成文章长截图
        
        Args:
            article_url: 文章URL
            article_title: 文章标题
            
        Returns:
            生成的截图文件路径
        """
        try:
            # 尝试使用playwright生成截图
            return self._generate_with_playwright(article_url, article_title)
        except ImportError:
            logger.warning("playwright未安装，尝试使用selenium")
            try:
                return self._generate_with_selenium(article_url, article_title)
            except ImportError:
                logger.warning("selenium未安装，使用简单HTML生成")
                return self._generate_simple_html(article_url, article_title)
        except Exception as e:
            logger.error(f"截图生成失败: {e}")
            # 降级到简单HTML生成
            return self._generate_simple_html(article_url, article_title)
    
    def _generate_with_playwright(self, article_url: str, article_title: str = None) -> str:
        """
        使用playwright生成长截图
        
        Args:
            article_url: 文章URL
            article_title: 文章标题
            
        Returns:
            截图文件路径
        """
        try:
            from playwright.sync_api import sync_playwright
            
            # 生成文件名
            safe_title = self._sanitize_filename(article_title or "CSDN文章")
            filename = f"{safe_title}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            filepath = os.path.join(self.output_dir, filename)
            
            with sync_playwright() as p:
                # 启动浏览器
                browser = p.chromium.launch(headless=True)
                page = browser.new_page()
                
                # 设置视口大小
                page.set_viewport_size({"width": 1200, "height": 800})
                
                # 访问页面
                page.goto(article_url, wait_until="networkidle", timeout=30000)
                
                # 等待页面加载完成
                page.wait_for_timeout(3000)
                
                # 生成全页面截图
                page.screenshot(path=filepath, full_page=True)
                
                browser.close()
            
            logger.info(f"Playwright截图生成成功: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Playwright截图生成失败: {e}")
            raise
    
    def _generate_with_selenium(self, article_url: str, article_title: str = None) -> str:
        """
        使用selenium生成长截图
        
        Args:
            article_url: 文章URL
            article_title: 文章标题
            
        Returns:
            截图文件路径
        """
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            
            # 生成文件名
            safe_title = self._sanitize_filename(article_title or "CSDN文章")
            filename = f"{safe_title}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            filepath = os.path.join(self.output_dir, filename)
            
            # 配置Chrome选项
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--window-size=1200,800')
            
            # 启动浏览器
            driver = webdriver.Chrome(options=chrome_options)
            
            try:
                # 访问页面
                driver.get(article_url)
                
                # 等待页面加载
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
                
                # 滚动到页面底部以确保所有内容加载
                driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                driver.implicitly_wait(2)
                driver.execute_script("window.scrollTo(0, 0);")
                
                # 获取页面总高度
                total_height = driver.execute_script("return document.body.scrollHeight")
                driver.set_window_size(1200, total_height)
                
                # 生成截图
                driver.save_screenshot(filepath)
                
            finally:
                driver.quit()
            
            logger.info(f"Selenium截图生成成功: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Selenium截图生成失败: {e}")
            raise
    
    def _generate_simple_html(self, article_url: str, article_title: str = None) -> str:
        """
        生成简单HTML文件（当截图生成失败时的降级方案）
        
        Args:
            article_url: 文章URL
            article_title: 文章标题
            
        Returns:
            HTML文件路径
        """
        try:
            # 生成文件名
            safe_title = self._sanitize_filename(article_title or "CSDN文章")
            filename = f"{safe_title}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
            filepath = os.path.join(self.output_dir, filename)
            
            # 生成简单的HTML文件
            html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{article_title or 'CSDN文章'}</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }}
        .header {{
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }}
        .url {{
            color: #666;
            word-break: break-all;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>{article_title or 'CSDN文章'}</h1>
        <p class="url">原文链接: <a href="{article_url}" target="_blank">{article_url}</a></p>
        <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    <div class="content">
        <p>由于截图功能不可用，请点击上方链接访问原文。</p>
        <p>或者您可以手动访问以下链接：</p>
        <p><strong>{article_url}</strong></p>
    </div>
</body>
</html>
            """
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"HTML文件生成成功: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"HTML文件生成失败: {e}")
            raise
    
    def _sanitize_filename(self, filename: str) -> str:
        """
        清理文件名，移除不合法字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            清理后的文件名
        """
        # 移除或替换不合法字符
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # 限制长度
        if len(filename) > 50:  # 缩短文件名长度
            filename = filename[:50]
        
        return filename.strip()
    
    def cleanup_old_files(self, max_age_hours: int = 24):
        """
        清理旧文件
        
        Args:
            max_age_hours: 文件最大保存时间（小时）
        """
        try:
            current_time = datetime.now()
            
            for filename in os.listdir(self.output_dir):
                filepath = os.path.join(self.output_dir, filename)
                
                # 检查文件修改时间
                file_time = datetime.fromtimestamp(os.path.getmtime(filepath))
                age_hours = (current_time - file_time).total_seconds() / 3600
                
                if age_hours > max_age_hours:
                    try:
                        os.remove(filepath)
                        logger.info(f"清理旧文件: {filepath}")
                    except Exception as e:
                        logger.warning(f"清理文件失败 {filepath}: {e}")
                        
        except Exception as e:
            logger.error(f"清理旧文件失败: {e}")

def create_screenshot_generator(output_dir: str = None) -> ScreenshotGenerator:
    """
    创建截图生成器实例
    
    Args:
        output_dir: 输出目录
        
    Returns:
        截图生成器实例
    """
    return ScreenshotGenerator(output_dir)
